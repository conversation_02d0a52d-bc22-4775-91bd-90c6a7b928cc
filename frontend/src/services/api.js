import axios from 'axios';

// API请求的基本URL
const API_BASE_URL = 'http://localhost:8000/api';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 处理API错误响应的统一函数
const handleApiError = async (error) => {
  if (error.response && error.response.data) {
    const errorData = error.response.data;

    // 如果是新的错误格式
    if (errorData.error && errorData.message) {
      const enhancedError = new Error(errorData.message);
      enhancedError.code = errorData.error_code;
      enhancedError.timestamp = errorData.timestamp;
      enhancedError.status = error.response.status;
      return enhancedError;
    }

    // 如果是字符串错误信息
    if (typeof errorData === 'string') {
      return new Error(errorData);
    }

    // 如果有detail字段（旧格式兼容）
    if (errorData.detail) {
      return new Error(errorData.detail);
    }
  }

  // 对于fetch请求的错误处理
  if (error instanceof Response && !error.ok) {
    try {
      const errorData = await error.json();
      if (errorData.error && errorData.message) {
        const enhancedError = new Error(errorData.message);
        enhancedError.code = errorData.error_code;
        enhancedError.timestamp = errorData.timestamp;
        enhancedError.status = error.status;
        return enhancedError;
      }
    } catch (parseError) {
      // 如果无法解析JSON，返回基本错误信息
      return new Error(`服务器响应错误: ${error.status} ${error.statusText}`);
    }
  }

  // 默认错误处理
  return error;
};

// API函数

// 获取可用的AI模型列表
export const getAvailableModels = async () => {
  try {
    console.log('API 服务: 获取可用模型列表...');
    const response = await api.get('/test-cases/models');
    console.log('API 服务: 模型列表响应:', response.data);
    return response.data;
  } catch (error) {
    console.error('API 服务: 获取模型列表错误:', error);
    const enhancedError = await handleApiError(error);
    throw enhancedError;
  }
};

export const generateTestCases = async (formData) => {
  try {
    console.log('API 服务: 发送请求到后端...');
    console.log('API 服务: 请求数据:', formData);

    // 使用原生 fetch 而不是 axios 来处理流式响应
    const response = await fetch(`${API_BASE_URL}/test-cases/generate`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const enhancedError = await handleApiError(response);
      throw enhancedError;
    }

    return response;
  } catch (error) {
    console.error('API 服务: 生成测试用例错误:', error);
    throw error;
  }
};

// 生成思维导图
export const generateMindMap = async (testCases) => {
  try {
    console.log('API 服务: 生成思维导图...');

    const response = await api.post('/test-cases/generate-mindmap', { test_cases: testCases });

    if (response.status === 200) {
      return response.data;
    } else {
      throw new Error(`生成思维导图失败: ${response.status}`);
    }
  } catch (error) {
    console.error('API 服务: 生成思维导图错误:', error);
    const enhancedError = await handleApiError(error);
    throw enhancedError;
  }
};

export const exportToExcel = async (testCases) => {
  try {
    const response = await api.post('/test-cases/export', testCases, {
      responseType: 'blob', // 对于文件下载很重要
    });
    return response;
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    const enhancedError = await handleApiError(error);
    throw enhancedError;
  }
};

// 导出到XMind
export const exportToXMind = async (testCases) => {
  try {
    console.log('API 服务: 导出到XMind...');

    const response = await api.post('/test-cases/export-xmind', testCases, {
      responseType: 'blob', // 对于文件下载很重要
    });
    return response;
  } catch (error) {
    console.error('Error exporting to XMind:', error);
    const enhancedError = await handleApiError(error);
    throw enhancedError;
  }
};

export const pingServer = async () => {
  try {
    console.log('Pinging server...');
    const response = await fetch(`${API_BASE_URL}/ping`);

    if (response.ok) {
      const data = await response.json();
      console.log('Server ping successful:', data);
      return { status: 'connected', data };
    } else {
      const enhancedError = await handleApiError(response);
      console.error('Server ping failed:', enhancedError);
      return { status: 'error', error: enhancedError.message };
    }
  } catch (error) {
    console.error('Server ping error:', error);
    return { status: 'error', error: error.message };
  }
};

// 健康检查 - 获取详细的系统状态
export const checkHealth = async () => {
  try {
    console.log('Checking server health...');
    const response = await fetch(`${API_BASE_URL}/health`);

    if (response.ok) {
      const data = await response.json();
      console.log('Health check successful:', data);
      return { status: 'healthy', data };
    } else {
      const enhancedError = await handleApiError(response);
      console.error('Health check failed:', enhancedError);
      return {
        status: 'unhealthy',
        error: enhancedError.message,
        code: enhancedError.code
      };
    }
  } catch (error) {
    console.error('Health check error:', error);
    return { status: 'error', error: error.message };
  }
};

export default api;
