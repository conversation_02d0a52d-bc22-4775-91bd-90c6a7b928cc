import React from 'react';
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import Container from '@mui/material/Container';
import Stack from '@mui/material/Stack';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import PsychologyIcon from '@mui/icons-material/Psychology';

const Header = ({ serverStatus = 'checking' }) => {
  return (
    <AppBar
      position="static"
      elevation={0}
      sx={{
        background: 'linear-gradient(135deg, #1a73e8 0%, #4285f4 50%, #8430ce 100%)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
      }}
    >
      <Container maxWidth="xl">
        <Toolbar sx={{ py: 1, minHeight: '72px !important' }}>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            justifyContent: 'space-between',
            flexWrap: 'wrap',
            gap: 2
          }}>
            {/* Logo和标题区域 */}
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              minWidth: 'fit-content'
            }}>
              {/* Logo图标组合 */}
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                p: 1,
                borderRadius: 2,
                background: 'rgba(255, 255, 255, 0.15)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
              }}>
                <PsychologyIcon sx={{
                  fontSize: 28,
                  color: '#fff',
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))'
                }} />
                <AutoAwesomeIcon sx={{
                  fontSize: 20,
                  color: '#ffd700',
                  filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))'
                }} />
              </Box>

              {/* 标题文字 */}
              <Stack spacing={0}>
                <Typography
                  variant="h5"
                  component="div"
                  sx={{
                    fontWeight: 700,
                    color: '#fff',
                    textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                    letterSpacing: '0.5px',
                    lineHeight: 1.2,
                    fontSize: { xs: '1.1rem', sm: '1.25rem', md: '1.5rem' }
                  }}
                >
                  爱集微智能测试平台
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: 'rgba(255, 255, 255, 0.9)',
                    fontWeight: 500,
                    letterSpacing: '1px',
                    textTransform: 'uppercase',
                    fontSize: { xs: '0.65rem', sm: '0.7rem', md: '0.75rem' }
                  }}
                >
                  Multi-Model AI Test Case Generator 2.0
                </Typography>
              </Stack>
            </Box>

            {/* 状态指示器 */}
            <Chip
              icon={
                serverStatus === 'connected' ? <CheckCircleIcon /> :
                serverStatus === 'error' ? <ErrorIcon /> :
                <HourglassEmptyIcon />
              }
              label={
                serverStatus === 'connected' ? '服务已连接' :
                serverStatus === 'error' ? '连接失败' :
                '连接检测中...'
              }
              color={
                serverStatus === 'connected' ? 'success' :
                serverStatus === 'error' ? 'error' :
                'default'
              }
              variant="filled"
              size="small"
              sx={{
                background: serverStatus === 'connected'
                  ? 'rgba(76, 175, 80, 0.9)'
                  : serverStatus === 'error'
                  ? 'rgba(244, 67, 54, 0.9)'
                  : 'rgba(255, 255, 255, 0.2)',
                color: '#fff',
                fontWeight: 600,
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                '& .MuiChip-icon': {
                  color: '#fff'
                },
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-1px)',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.2)'
                }
              }}
            />
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
};

export default Header;
