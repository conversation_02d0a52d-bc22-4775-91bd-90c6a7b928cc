import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  AutoAwesome,
  Speed,
  Security,
  TrendingUp,
  CheckCircle,
  Psychology,
  CloudUpload,
  Analytics,
  AccountTree,
  Savings
} from '@mui/icons-material';

const ProductIntro = () => {
  // 注释掉多余的功能模块，只保留智能测试用例生成工作台核心内容
  // const features = [
  //   {
  //     icon: <AutoAwesome sx={{ color: '#1a73e8' }} />,
  //     title: 'AI驱动智能生成',
  //     description: '基于先进的多模态AI模型，支持图像、PDF、API文档等多种输入格式'
  //   },
  //   {
  //     icon: <Speed sx={{ color: '#34a853' }} />,
  //     title: '极速生成效率',
  //     description: '秒级生成完整测试用例，相比传统人工编写提升效率90%以上'
  //   },
  //   {
  //     icon: <Security sx={{ color: '#ea4335' }} />,
  //     title: '质量保证体系',
  //     description: '智能覆盖正向、负向、边界等多种测试场景，确保测试完整性'
  //   },
  //   {
  //     icon: <AccountTree sx={{ color: '#fbbc04' }} />,
  //     title: '无缝集成能力',
  //     description: '支持Excel导出、思维导图可视化，轻松集成现有测试流程'
  //   }
  // ];

  // const businessValues = [
  //   {
  //     icon: <TrendingUp />,
  //     title: '提升测试效率',
  //     value: '90%+',
  //     description: '相比传统手工编写，大幅提升测试用例生成效率'
  //   },
  //   {
  //     icon: <Savings />,
  //     title: '降低人力成本',
  //     value: '60%+',
  //     description: '减少测试用例编写的人力投入，释放团队创造力'
  //   },
  //   {
  //     icon: <Analytics />,
  //     title: '提高覆盖率',
  //     value: '95%+',
  //     description: 'AI智能分析确保测试场景覆盖更全面、更准确'
  //   },
  //   {
  //     icon: <Psychology />,
  //     title: '质量一致性',
  //     value: '100%',
  //     description: '标准化测试用例格式，消除人为差异和遗漏'
  //   }
  // ];

  // const techAdvantages = [
  //   '🤖 多模态AI技术：支持图像识别、文档解析、API分析',
  //   '⚡ 实时流式输出：边生成边展示，用户体验流畅',
  //   '🎯 智能模型选择：根据文件类型自动选择最优AI模型',
  //   '📊 可视化展示：支持思维导图、表格等多种展示形式',
  //   '🔄 标准化输出：统一的测试用例格式，便于管理和执行',
  //   '☁️ 云端部署：支持私有化部署，数据安全可控'
  // ];

  // return (
    // <Box sx={{ mb: 4 }}>
    //   {/* 智能测试用例生成工作台 */}
    //   <Paper
    //     elevation={0}
    //     sx={{
    //       p: 4,
    //       mb: 3,
    //       background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    //       color: 'white',
    //       borderRadius: 3
    //     }}
    //   >
    //     <Typography variant="h4" component="h2" gutterBottom sx={{ fontWeight: 600 }}>
    //       🚀 智能测试用例生成工作台
    //     </Typography>
    //     <Typography variant="h6" sx={{ mb: 2, opacity: 0.9 }}>
    //       革命性的AI驱动测试用例生成解决方案
    //     </Typography>
    //     <Typography variant="body1" sx={{ opacity: 0.8, maxWidth: '800px' }}>
    //       基于先进的多模态人工智能技术，支持图像、PDF文档、OpenAPI规范等多种输入格式，
    //       智能生成高质量、全覆盖的测试用例，助力企业数字化转型和质量提升。
    //     </Typography>
    //   </Paper>
    //
    //   {/* 注释掉多余的模块，只保留智能测试用例生成工作台 */}
    // </Box>
  // );
};

export default ProductIntro;
