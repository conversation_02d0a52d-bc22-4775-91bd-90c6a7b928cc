import React from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  <PERSON>,
  Divider,
  Chip,
  Stack
} from '@mui/material';
import {
  GitHub,
  Email,
  Phone,
  LocationOn,
  AutoAwesome,
  Security,
  Speed,
  CloudUpload
} from '@mui/icons-material';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const features = [
    { icon: <AutoAwesome />, label: 'AI驱动' },
    { icon: <Speed />, label: '高效生成' },
    { icon: <Security />, label: '质量保证' },
    { icon: <CloudUpload />, label: '多格式支持' }
  ];

  return (
    <Box
      component="footer"
      sx={{
        bgcolor: '#1a1a1a',
        color: 'white',
        py: 6,
        mt: 8
      }}
    >
      <Container maxWidth="lg">
        {/*<Grid container spacing={4}>*/}
        {/*  /!* 产品信息 *!/*/}
        {/*  <Grid item xs={12} md={4}>*/}
        {/*    <Box sx={{ mb: 3 }}>*/}
        {/*      <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>*/}
        {/*        🚀 AI测试用例生成平台*/}
        {/*      </Typography>*/}
        {/*      <Typography variant="body2" sx={{ mb: 3, opacity: 0.8, lineHeight: 1.6 }}>*/}
        {/*        基于先进的多模态人工智能技术，为企业提供智能化的测试用例生成解决方案，*/}
        {/*        大幅提升测试效率，保障软件质量。*/}
        {/*      </Typography>*/}

        {/*      <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>*/}
        {/*        {features.map((feature, index) => (*/}
        {/*          <Chip*/}
        {/*            key={index}*/}
        {/*            icon={feature.icon}*/}
        {/*            label={feature.label}*/}
        {/*            size="small"*/}
        {/*            sx={{*/}
        {/*              bgcolor: 'rgba(255, 255, 255, 0.1)',*/}
        {/*              color: 'white',*/}
        {/*              '& .MuiChip-icon': { color: 'white' }*/}
        {/*            }}*/}
        {/*          />*/}
        {/*        ))}*/}
        {/*      </Stack>*/}
        {/*    </Box>*/}
        {/*  </Grid>*/}

        {/*  /!* 产品特性 *!/*/}
        {/*  <Grid item xs={12} md={3}>*/}
        {/*    <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>*/}
        {/*      产品特性*/}
        {/*    </Typography>*/}
        {/*    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>*/}
        {/*      <Link href="#" color="inherit" sx={{ opacity: 0.8, textDecoration: 'none', '&:hover': { opacity: 1 } }}>*/}
        {/*        • 多模态AI识别*/}
        {/*      </Link>*/}
        {/*      <Link href="#" color="inherit" sx={{ opacity: 0.8, textDecoration: 'none', '&:hover': { opacity: 1 } }}>*/}
        {/*        • 实时流式生成*/}
        {/*      </Link>*/}
        {/*      <Link href="#" color="inherit" sx={{ opacity: 0.8, textDecoration: 'none', '&:hover': { opacity: 1 } }}>*/}
        {/*        • 思维导图可视化*/}
        {/*      </Link>*/}
        {/*      <Link href="#" color="inherit" sx={{ opacity: 0.8, textDecoration: 'none', '&:hover': { opacity: 1 } }}>*/}
        {/*        • Excel一键导出*/}
        {/*      </Link>*/}
        {/*      <Link href="#" color="inherit" sx={{ opacity: 0.8, textDecoration: 'none', '&:hover': { opacity: 1 } }}>*/}
        {/*        • 私有化部署*/}
        {/*      </Link>*/}
        {/*    </Box>*/}
        {/*  </Grid>*/}

        {/*  /!* 支持格式 *!/*/}
        {/*  <Grid item xs={12} md={2}>*/}
        {/*    <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>*/}
        {/*      支持格式*/}
        {/*    </Typography>*/}
        {/*    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>*/}
        {/*      <Typography variant="body2" sx={{ opacity: 0.8 }}>*/}
        {/*        • 图像文件 (PNG, JPG, GIF)*/}
        {/*      </Typography>*/}
        {/*      <Typography variant="body2" sx={{ opacity: 0.8 }}>*/}
        {/*        • PDF需求文档*/}
        {/*      </Typography>*/}
        {/*      <Typography variant="body2" sx={{ opacity: 0.8 }}>*/}
        {/*        • OpenAPI规范*/}
        {/*      </Typography>*/}
        {/*      <Typography variant="body2" sx={{ opacity: 0.8 }}>*/}
        {/*        • Swagger文档*/}
        {/*      </Typography>*/}
        {/*      <Typography variant="body2" sx={{ opacity: 0.8 }}>*/}
        {/*        • YAML配置文件*/}
        {/*      </Typography>*/}
        {/*    </Box>*/}
        {/*  </Grid>*/}

        {/*  /!* 联系方式 *!/*/}
        {/*  <Grid item xs={12} md={3}>*/}
        {/*    <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>*/}
        {/*      联系我们*/}
        {/*    </Typography>*/}
        {/*    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>*/}
        {/*      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>*/}
        {/*        <Email sx={{ fontSize: 18, opacity: 0.8 }} />*/}
        {/*        <Typography variant="body2" sx={{ opacity: 0.8 }}>*/}
        {/*          <EMAIL>*/}
        {/*        </Typography>*/}
        {/*      </Box>*/}

        {/*      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>*/}
        {/*        <Phone sx={{ fontSize: 18, opacity: 0.8 }} />*/}
        {/*        <Typography variant="body2" sx={{ opacity: 0.8 }}>*/}
        {/*          *************/}
        {/*        </Typography>*/}
        {/*      </Box>*/}

        {/*      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>*/}
        {/*        <LocationOn sx={{ fontSize: 18, opacity: 0.8 }} />*/}
        {/*        <Typography variant="body2" sx={{ opacity: 0.8 }}>*/}
        {/*          北京市海淀区中关村软件园*/}
        {/*        </Typography>*/}
        {/*      </Box>*/}

        {/*      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>*/}
        {/*        <GitHub sx={{ fontSize: 18, opacity: 0.8 }} />*/}
        {/*        <Link*/}
        {/*          href="#"*/}
        {/*          color="inherit"*/}
        {/*          sx={{*/}
        {/*            opacity: 0.8,*/}
        {/*            textDecoration: 'none',*/}
        {/*            '&:hover': { opacity: 1 }*/}
        {/*          }}*/}
        {/*        >*/}
        {/*          GitHub开源项目*/}
        {/*        </Link>*/}
        {/*      </Box>*/}
        {/*    </Box>*/}
        {/*  </Grid>*/}
        {/*</Grid>*/}

        {/*<Divider sx={{ my: 4, borderColor: 'rgba(255, 255, 255, 0.1)' }} />*/}

        {/* 底部信息 */}
        {/*<Grid container spacing={2} alignItems="center">*/}
        {/*  <Grid item xs={12} md={6}>*/}
        {/*    <Typography variant="body2" sx={{ opacity: 0.6 }}>*/}
        {/*      © {currentYear} AI测试用例生成平台. 保留所有权利.*/}
        {/*    </Typography>*/}
        {/*  </Grid>*/}

        {/*</Grid>*/}

        {/* 技术说明 */}
        <Box
          sx={{
            mt: 1,
            p: 3,
            bgcolor: 'rgba(255, 255, 255, 0.05)',
            borderRadius: 2,
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}
        >
          <Typography variant="body2" sx={{ opacity: 0.7, textAlign: 'center', lineHeight: 1.6 }}>
            本平台采用先进的多模态AI技术，包括计算机视觉、自然语言处理和知识图谱等技术，
            提供智能化、自动化的测试用例生成服务，助力数字化转型和质量提升。
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
