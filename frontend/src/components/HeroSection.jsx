import React from 'react';
import {
  <PERSON>,
  Typography,
  Container,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  Avatar,
  Stack
} from '@mui/material';
import {
  AutoAwesome,
  TrendingUp,
  Speed,
  Security,
  CloudUpload,
  Description,
  Image,
  Code,
  PlayArrow
} from '@mui/icons-material';

const HeroSection = ({ onScrollToUpload }) => {
//   const supportedFormats = [
//     { icon: <Image />, label: '图像文件', formats: 'PNG, JPG, JPEG, GIF' },
//     { icon: <Description />, label: 'PDF文档', formats: '需求文档, 设计文档' },
//     { icon: <Code />, label: 'API文档', formats: 'OpenAPI, Swagger, YAML' }
//   ];

//   const stats = [
//     { value: '10,000+', label: '测试用例生成' },
//     { value: '500+', label: '企业用户' },
//     { value: '99.9%', label: '服务可用性' },
//     { value: '90%+', label: '效率提升' }
//   ];

//   return (
//     <Box
//       sx={{
//         background: 'linear-gradient(135deg, #1a73e8 0%, #4285f4 50%, #8430ce 100%)',
//         color: 'white',
//         py: 8,
//         position: 'relative',
//         overflow: 'hidden'
//       }}
//     >
//       {/* 背景装饰 */}
//       <Box
//         sx={{
//           position: 'absolute',
//           top: 0,
//           left: 0,
//           right: 0,
//           bottom: 0,
//           opacity: 0.1,
//           background: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
//         }}
//       />

//       <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
//         <Grid container spacing={6} alignItems="center">
//           <Grid item xs={12} md={6}>
//             <Box sx={{ mb: 4 }}>
//               <Chip
//                 icon={<AutoAwesome />}
//                 label="AI驱动 · 智能生成"
//                 sx={{
//                   bgcolor: 'rgba(255, 255, 255, 0.2)',
//                   color: 'white',
//                   mb: 3,
//                   fontWeight: 600,
//                   '& .MuiChip-icon': { color: 'white' }
//                 }}
//               />

//               <Typography
//                 variant="h2"
//                 component="h1"
//                 sx={{
//                   fontWeight: 700,
//                   mb: 3,
//                   textShadow: '0 2px 4px rgba(0,0,0,0.3)',
//                   lineHeight: 1.2
//                 }}
//               >
//                 爱集微智能测试平台
//               </Typography>

//               <Typography
//                 variant="h5"
//                 sx={{
//                   mb: 4,
//                   opacity: 0.9,
//                   fontWeight: 400,
//                   lineHeight: 1.4
//                 }}
//               >
//                 多模型驱动，从文档到测试用例的智能转换
//               </Typography>

//               <Button
//                 variant="contained"
//                 size="large"
//                 startIcon={<PlayArrow />}
//                 onClick={onScrollToUpload}
//                 sx={{
//                   bgcolor: 'rgba(255, 255, 255, 0.2)',
//                   color: 'white',
//                   fontWeight: 600,
//                   py: 1.5,
//                   px: 4,
//                   borderRadius: 3,
//                   backdropFilter: 'blur(10px)',
//                   border: '1px solid rgba(255, 255, 255, 0.3)',
//                   '&:hover': {
//                     bgcolor: 'rgba(255, 255, 255, 0.3)',
//                     transform: 'translateY(-2px)',
//                     boxShadow: '0 8px 24px rgba(0,0,0,0.2)'
//                   }
//                 }}
//               >
//                 立即开始
//               </Button>
//             </Box>
//           </Grid>

//           <Grid item xs={12} md={6}>
//             <Box sx={{ textAlign: 'center' }}>
//               {/* 数据统计 */}
//               <Grid container spacing={3} sx={{ mb: 4 }}>
//                 {stats.map((stat, index) => (
//                   <Grid item xs={6} key={index}>
//                     <Card
//                       elevation={0}
//                       sx={{
//                         bgcolor: 'rgba(255, 255, 255, 0.15)',
//                         backdropFilter: 'blur(10px)',
//                         border: '1px solid rgba(255, 255, 255, 0.2)',
//                         borderRadius: 3,
//                         transition: 'all 0.3s ease',
//                         '&:hover': {
//                           transform: 'translateY(-4px)',
//                           boxShadow: '0 12px 24px rgba(0,0,0,0.2)'
//                         }
//                       }}
//                     >
//                       <CardContent sx={{ textAlign: 'center', py: 3 }}>
//                         <Typography
//                           variant="h4"
//                           sx={{
//                             fontWeight: 700,
//                             color: '#ffd700',
//                             mb: 1,
//                             textShadow: '0 2px 4px rgba(0,0,0,0.2)'
//                           }}
//                         >
//                           {stat.value}
//                         </Typography>
//                         <Typography
//                           variant="body2"
//                           sx={{ color: 'white', opacity: 0.9 }}
//                         >
//                           {stat.label}
//                         </Typography>
//                       </CardContent>
//                     </Card>
//                   </Grid>
//                 ))}
//               </Grid>

//               {/* 支持格式 */}
//               <Box>
//                 <Typography variant="body2" sx={{ mb: 2, opacity: 0.8, fontWeight: 500 }}>
//                   支持多种输入格式：
//                 </Typography>
//                 <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap sx={{ justifyContent: 'center' }}>
//                   {supportedFormats.map((format, index) => (
//                     <Chip
//                       key={index}
//                       icon={format.icon}
//                       label={format.label}
//                       size="small"
//                       sx={{
//                         bgcolor: 'rgba(255, 255, 255, 0.15)',
//                         color: 'white',
//                         mb: 1,
//                         '& .MuiChip-icon': { color: 'white' },
//                         backdropFilter: 'blur(10px)',
//                         border: '1px solid rgba(255, 255, 255, 0.2)',
//                       }}
//                     />
//                   ))}
//                 </Stack>
//               </Box>
//             </Box>
//           </Grid>
//         </Grid>
//       </Container>
//     </Box>
//   );
};

export default HeroSection;
