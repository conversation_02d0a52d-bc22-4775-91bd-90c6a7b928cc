@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM AI测试用例生成器 - 前后端启动脚本 (Windows版)
REM 作者: AI Assistant
REM 版本: 1.0.0

REM 项目根目录
set "PROJECT_ROOT=%~dp0"
set "BACKEND_DIR=%PROJECT_ROOT%backend"
set "FRONTEND_DIR=%PROJECT_ROOT%frontend"

REM 日志目录
set "LOG_DIR=%PROJECT_ROOT%logs"
set "BACKEND_LOG=%LOG_DIR%\backend.log"
set "FRONTEND_LOG=%LOG_DIR%\frontend.log"

REM PID文件目录
set "PID_DIR=%PROJECT_ROOT%.pids"
set "BACKEND_PID=%PID_DIR%\backend.pid"
set "FRONTEND_PID=%PID_DIR%\frontend.pid"

REM 创建必要的目录
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"
if not exist "%PID_DIR%" mkdir "%PID_DIR%"

REM 颜色定义 (Windows 10+)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "PURPLE=[95m"
set "CYAN=[96m"
set "NC=[0m"

REM 打印带颜色的消息
:print_message
echo %~1%~2%NC%
goto :eof

REM 打印标题
:print_title
echo.
call :print_message "%CYAN%" "=================================="
call :print_message "%CYAN%" "%~1"
call :print_message "%CYAN%" "=================================="
echo.
goto :eof

REM 检查命令是否存在
:check_command
where %1 >nul 2>&1
if errorlevel 1 (
    call :print_message "%RED%" "❌ 错误: %1 未安装或不在PATH中"
    exit /b 1
)
exit /b 0

REM 检查端口是否被占用
:check_port
netstat -an | findstr ":%1 " | findstr "LISTENING" >nul 2>&1
if not errorlevel 1 (
    call :print_message "%YELLOW%" "⚠️  警告: 端口 %1 已被占用"
    exit /b 1
)
exit /b 0

REM 检查进程是否运行
:is_process_running
if not exist "%~1" exit /b 1
set /p pid=<"%~1"
tasklist /fi "pid eq %pid%" 2>nul | findstr "%pid%" >nul
if errorlevel 1 (
    del "%~1" 2>nul
    exit /b 1
)
exit /b 0

REM 停止进程
:stop_process
call :is_process_running "%~1"
if not errorlevel 1 (
    set /p pid=<"%~1"
    call :print_message "%YELLOW%" "正在停止 %~2 (PID: !pid!)..."
    taskkill /pid !pid! /f >nul 2>&1
    del "%~1" 2>nul
    call :print_message "%GREEN%" "✅ %~2 已停止"
) else (
    call :print_message "%YELLOW%" "%~2 未运行"
)
goto :eof

REM 检查环境
:check_environment
call :print_title "检查运行环境"

REM 检查Python
call :check_command python
if errorlevel 1 (
    call :print_message "%RED%" "请安装Python 3.8+"
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
call :print_message "%GREEN%" "✅ Python: !python_version!"

REM 检查Node.js
call :check_command node
if errorlevel 1 (
    call :print_message "%RED%" "请安装Node.js 16+"
    exit /b 1
)

for /f %%i in ('node --version') do set node_version=%%i
call :print_message "%GREEN%" "✅ Node.js: !node_version!"

REM 检查npm
call :check_command npm
if errorlevel 1 (
    call :print_message "%RED%" "请安装npm"
    exit /b 1
)

for /f %%i in ('npm --version') do set npm_version=%%i
call :print_message "%GREEN%" "✅ npm: !npm_version!"

REM 检查项目目录
if not exist "%BACKEND_DIR%" (
    call :print_message "%RED%" "❌ 后端目录不存在: %BACKEND_DIR%"
    exit /b 1
)

if not exist "%FRONTEND_DIR%" (
    call :print_message "%RED%" "❌ 前端目录不存在: %FRONTEND_DIR%"
    exit /b 1
)

call :print_message "%GREEN%" "✅ 项目目录检查完成"
goto :eof

REM 安装后端依赖
:install_backend_deps
call :print_title "安装后端依赖"

cd /d "%BACKEND_DIR%"

if not exist "requirements.txt" (
    call :print_message "%RED%" "❌ requirements.txt 不存在"
    exit /b 1
)

if not exist "%PROJECT_ROOT%\.venv" (
    call :print_message "%YELLOW%" "创建Python虚拟环境..."
    cd /d "%PROJECT_ROOT%"
    python -m venv .venv
    cd /d "%BACKEND_DIR%"
)

call :print_message "%BLUE%" "激活虚拟环境..."
call "%PROJECT_ROOT%\.venv\Scripts\activate.bat"

call :print_message "%BLUE%" "升级pip..."
python -m pip install --upgrade pip

call :print_message "%BLUE%" "安装Python依赖..."
pip install -r requirements.txt

call :print_message "%GREEN%" "✅ 后端依赖安装完成"
goto :eof

REM 安装前端依赖
:install_frontend_deps
call :print_title "安装前端依赖"

cd /d "%FRONTEND_DIR%"

if not exist "package.json" (
    call :print_message "%RED%" "❌ package.json 不存在"
    exit /b 1
)

call :print_message "%BLUE%" "安装Node.js依赖..."
npm install

call :print_message "%GREEN%" "✅ 前端依赖安装完成"
goto :eof

REM 清空上传目录
:clear_uploads_directory
set "uploads_dir=%BACKEND_DIR%\uploads"

if exist "%uploads_dir%" (
    call :print_message "%BLUE%" "清空上传目录: %uploads_dir%"
    REM 删除目录中的所有文件和子目录
    del /q /s "%uploads_dir%\*" >nul 2>&1
    for /d %%i in ("%uploads_dir%\*") do rmdir /s /q "%%i" >nul 2>&1
    call :print_message "%GREEN%" "✅ 上传目录已清空"
) else (
    call :print_message "%YELLOW%" "⚠️  上传目录不存在: %uploads_dir%"
    mkdir "%uploads_dir%" >nul 2>&1
    call :print_message "%GREEN%" "✅ 上传目录已创建"
)
goto :eof

REM 启动后端
:start_backend
call :print_title "启动后端服务"

REM 清空上传目录
call :clear_uploads_directory

cd /d "%BACKEND_DIR%"

call :check_port 8000
if not errorlevel 1 (
    call :print_message "%RED%" "❌ 后端端口8000被占用，请先停止相关进程"
    exit /b 1
)

if not exist ".env" (
    call :print_message "%YELLOW%" "⚠️  .env文件不存在"
    if exist ".env.example" (
        call :print_message "%BLUE%" "请复制.env.example为.env并配置相应变量"
        call :print_message "%BLUE%" "copy .env.example .env"
    )
    set /p continue="是否继续启动？(y/N): "
    if /i not "!continue!"=="y" exit /b 1
)

call :print_message "%BLUE%" "激活虚拟环境..."
call "%PROJECT_ROOT%\.venv\Scripts\activate.bat"

call :print_message "%BLUE%" "启动FastAPI服务器..."
start /b python start.py > "%BACKEND_LOG%" 2>&1

REM 获取进程ID (Windows方法)
timeout /t 2 /nobreak >nul
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo csv ^| findstr start.py') do (
    echo %%i > "%BACKEND_PID%"
    goto backend_started
)

:backend_started
call :print_message "%BLUE%" "等待后端服务启动..."
timeout /t 3 /nobreak >nul

call :is_process_running "%BACKEND_PID%"
if not errorlevel 1 (
    set /p backend_pid=<"%BACKEND_PID%"
    call :print_message "%GREEN%" "✅ 后端服务启动成功 (PID: !backend_pid!)"
    call :print_message "%GREEN%" "   服务地址: http://localhost:8000"
    call :print_message "%GREEN%" "   API文档: http://localhost:8000/docs"
    call :print_message "%GREEN%" "   日志文件: %BACKEND_LOG%"
) else (
    call :print_message "%RED%" "❌ 后端服务启动失败"
    call :print_message "%RED%" "请查看日志: %BACKEND_LOG%"
    exit /b 1
)
goto :eof

REM 启动前端
:start_frontend
call :print_title "启动前端服务"

cd /d "%FRONTEND_DIR%"

call :check_port 3000
if not errorlevel 1 (
    call :print_message "%YELLOW%" "⚠️  前端端口3000被占用，React将尝试使用其他端口"
)

call :print_message "%BLUE%" "启动React开发服务器..."
start /b npm start > "%FRONTEND_LOG%" 2>&1

REM 获取进程ID
timeout /t 2 /nobreak >nul
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq node.exe" /fo csv ^| findstr "npm"') do (
    echo %%i > "%FRONTEND_PID%"
    goto frontend_started
)

:frontend_started
call :print_message "%BLUE%" "等待前端服务启动..."
timeout /t 5 /nobreak >nul

call :is_process_running "%FRONTEND_PID%"
if not errorlevel 1 (
    set /p frontend_pid=<"%FRONTEND_PID%"
    call :print_message "%GREEN%" "✅ 前端服务启动成功 (PID: !frontend_pid!)"
    call :print_message "%GREEN%" "   服务地址: http://localhost:3000"
    call :print_message "%GREEN%" "   日志文件: %FRONTEND_LOG%"
) else (
    call :print_message "%RED%" "❌ 前端服务启动失败"
    call :print_message "%RED%" "请查看日志: %FRONTEND_LOG%"
    exit /b 1
)
goto :eof

REM 停止所有服务
:stop_all
call :print_title "停止所有服务"

call :stop_process "%BACKEND_PID%" "后端服务"
call :stop_process "%FRONTEND_PID%" "前端服务"

call :print_message "%GREEN%" "✅ 所有服务已停止"
goto :eof

REM 查看状态
:show_status
call :print_title "服务状态"

call :is_process_running "%BACKEND_PID%"
if not errorlevel 1 (
    set /p backend_pid=<"%BACKEND_PID%"
    call :print_message "%GREEN%" "✅ 后端服务运行中 (PID: !backend_pid!)"
    call :print_message "%GREEN%" "   服务地址: http://localhost:8000"
) else (
    call :print_message "%RED%" "❌ 后端服务未运行"
)

call :is_process_running "%FRONTEND_PID%"
if not errorlevel 1 (
    set /p frontend_pid=<"%FRONTEND_PID%"
    call :print_message "%GREEN%" "✅ 前端服务运行中 (PID: !frontend_pid!)"
    call :print_message "%GREEN%" "   服务地址: http://localhost:3000"
) else (
    call :print_message "%RED%" "❌ 前端服务未运行"
)
goto :eof

REM 查看日志
:show_logs
if "%~1"=="backend" (
    if exist "%BACKEND_LOG%" (
        call :print_message "%BLUE%" "后端日志 (最后50行):"
        powershell "Get-Content '%BACKEND_LOG%' -Tail 50"
    ) else (
        call :print_message "%YELLOW%" "后端日志文件不存在"
    )
) else if "%~1"=="frontend" (
    if exist "%FRONTEND_LOG%" (
        call :print_message "%BLUE%" "前端日志 (最后50行):"
        powershell "Get-Content '%FRONTEND_LOG%' -Tail 50"
    ) else (
        call :print_message "%YELLOW%" "前端日志文件不存在"
    )
) else (
    call :print_message "%BLUE%" "所有日志:"
    if exist "%BACKEND_LOG%" (
        call :print_message "%CYAN%" "=== 后端日志 (最后25行) ==="
        powershell "Get-Content '%BACKEND_LOG%' -Tail 25"
    )
    if exist "%FRONTEND_LOG%" (
        call :print_message "%CYAN%" "=== 前端日志 (最后25行) ==="
        powershell "Get-Content '%FRONTEND_LOG%' -Tail 25"
    )
)
goto :eof

REM 显示帮助信息
:show_help
call :print_message "%CYAN%" "AI测试用例生成器 - 启动脚本 (Windows版)"
echo.
call :print_message "%YELLOW%" "用法: %~nx0 [命令]"
echo.
call :print_message "%BLUE%" "可用命令:"
call :print_message "%GREEN%" "  start     启动前后端服务 (默认)"
call :print_message "%GREEN%" "  stop      停止所有服务"
call :print_message "%GREEN%" "  restart   重启所有服务"
call :print_message "%GREEN%" "  status    查看服务状态"
call :print_message "%GREEN%" "  install   安装依赖"
call :print_message "%GREEN%" "  logs      查看日志 [backend|frontend]"
call :print_message "%GREEN%" "  help      显示帮助信息"
echo.
call :print_message "%BLUE%" "示例:"
call :print_message "%YELLOW%" "  %~nx0              # 启动所有服务"
call :print_message "%YELLOW%" "  %~nx0 start        # 启动所有服务"
call :print_message "%YELLOW%" "  %~nx0 stop         # 停止所有服务"
call :print_message "%YELLOW%" "  %~nx0 status       # 查看状态"
call :print_message "%YELLOW%" "  %~nx0 logs backend # 查看后端日志"
echo.
goto :eof

REM 主函数
:main
set "command=%~1"
if "%command%"=="" set "command=start"

if "%command%"=="start" (
    call :check_environment
    if errorlevel 1 exit /b 1
    call :start_backend
    if errorlevel 1 exit /b 1
    call :start_frontend
    if errorlevel 1 exit /b 1
    echo.
    call :print_message "%GREEN%" "🎉 所有服务启动完成!"
    call :print_message "%BLUE%" "前端地址: http://localhost:3000"
    call :print_message "%BLUE%" "后端地址: http://localhost:8000"
    call :print_message "%BLUE%" "API文档: http://localhost:8000/docs"
    echo.
    call :print_message "%YELLOW%" "使用 '%~nx0 stop' 停止服务"
    call :print_message "%YELLOW%" "使用 '%~nx0 status' 查看状态"
    call :print_message "%YELLOW%" "使用 '%~nx0 logs' 查看日志"
) else if "%command%"=="stop" (
    call :stop_all
) else if "%command%"=="restart" (
    call :stop_all
    timeout /t 2 /nobreak >nul
    call :check_environment
    if errorlevel 1 exit /b 1
    call :start_backend
    if errorlevel 1 exit /b 1
    call :start_frontend
    if errorlevel 1 exit /b 1
    call :print_message "%GREEN%" "🎉 服务重启完成!"
) else if "%command%"=="status" (
    call :show_status
) else if "%command%"=="install" (
    call :check_environment
    if errorlevel 1 exit /b 1
    call :install_backend_deps
    if errorlevel 1 exit /b 1
    call :install_frontend_deps
    if errorlevel 1 exit /b 1
    call :print_message "%GREEN%" "🎉 依赖安装完成!"
) else if "%command%"=="logs" (
    call :show_logs %2
) else if "%command%"=="help" (
    call :show_help
) else if "%command%"=="-h" (
    call :show_help
) else if "%command%"=="--help" (
    call :show_help
) else (
    call :print_message "%RED%" "❌ 未知命令: %command%"
    call :show_help
    exit /b 1
)

goto :eof

REM 运行主函数
call :main %*
