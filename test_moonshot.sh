#!/bin/bash

echo "🚀 测试Moonshot模型集成..."

# 测试模型API端点
echo "📋 测试模型列表API..."
response=$(curl -s http://localhost:8000/api/test-cases/models)
if echo "$response" | grep -q "moonshot"; then
    echo "✅ Moonshot模型在API中可见"
    echo "$response" | python3 -c "
import sys, json
data = json.load(sys.stdin)
for model in data['available_models']:
    print(f'  - {model[\"name\"]} ({model[\"id\"]}): {model[\"description\"]}')
"
else
    echo "❌ Moonshot模型在API中不可见"
    exit 1
fi

echo ""
echo "🧪 测试模型选择功能..."

# 创建一个测试PDF文件
echo "创建测试文件..." > test.pdf

# 测试选择Moonshot模型
echo "📄 测试PDF文件 + Moonshot模型选择..."
response=$(curl -s -X POST \
  -F "file=@test.pdf" \
  -F "context=这是一个测试上下文，用于验证模型选择功能" \
  -F "requirements=生成简单的测试用例来验证功能" \
  -F "preferred_model=moonshot" \
  http://localhost:8000/api/test-cases/generate)

if echo "$response" | grep -q "使用模型: Moonshot"; then
    echo "✅ 成功选择Moonshot模型"
else
    echo "❌ 未能正确选择Moonshot模型"
    echo "响应内容:"
    echo "$response" | head -20
fi

# 清理测试文件
rm -f test.pdf

echo ""
echo "🎉 Moonshot模型集成测试完成！"
