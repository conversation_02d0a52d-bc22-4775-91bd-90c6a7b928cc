#!/bin/bash

# AI测试用例生成器 - 前后端启动脚本
# 作者: AI Assistant
# 版本: 1.0.0

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"

# 日志文件
LOG_DIR="$PROJECT_ROOT/logs"
BACKEND_LOG="$LOG_DIR/backend.log"
FRONTEND_LOG="$LOG_DIR/frontend.log"

# PID文件
PID_DIR="$PROJECT_ROOT/.pids"
BACKEND_PID="$PID_DIR/backend.pid"
FRONTEND_PID="$PID_DIR/frontend.pid"

# 创建必要的目录
mkdir -p "$LOG_DIR" "$PID_DIR"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo
    print_message $CYAN "=================================="
    print_message $CYAN "$1"
    print_message $CYAN "=================================="
    echo
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_message $RED "❌ 错误: $1 未安装或不在PATH中"
        return 1
    fi
    return 0
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_message $YELLOW "⚠️  警告: 端口 $port 已被占用"
        return 1
    fi
    return 0
}

# 检查进程是否运行
is_process_running() {
    local pid_file=$1
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            return 0
        else
            rm -f "$pid_file"
        fi
    fi
    return 1
}

# 根据端口杀死进程
kill_process_by_port() {
    local port=$1
    local service_name=$2

    print_message $YELLOW "正在查找占用端口 $port 的进程..."

    # 查找占用端口的进程
    local pids=$(lsof -ti :$port 2>/dev/null || true)

    if [ -n "$pids" ]; then
        print_message $YELLOW "发现占用端口 $port 的进程: $pids"

        for pid in $pids; do
            if ps -p $pid > /dev/null 2>&1; then
                print_message $YELLOW "正在停止进程 $pid ($service_name)..."

                # 尝试优雅停止
                kill -TERM $pid 2>/dev/null || true

                # 等待进程结束
                local count=0
                while ps -p $pid > /dev/null 2>&1 && [ $count -lt 5 ]; do
                    sleep 1
                    count=$((count + 1))
                done

                # 如果进程仍在运行，强制杀死
                if ps -p $pid > /dev/null 2>&1; then
                    print_message $YELLOW "强制停止进程 $pid..."
                    kill -KILL $pid 2>/dev/null || true
                    sleep 1
                fi

                if ! ps -p $pid > /dev/null 2>&1; then
                    print_message $GREEN "✅ 进程 $pid 已停止"
                else
                    print_message $RED "❌ 无法停止进程 $pid"
                fi
            fi
        done
    else
        print_message $GREEN "端口 $port 未被占用"
    fi
}

# 停止进程
stop_process() {
    local pid_file=$1
    local name=$2
    local port=$3

    # 首先尝试通过PID文件停止
    if is_process_running "$pid_file"; then
        local pid=$(cat "$pid_file")
        print_message $YELLOW "正在停止 $name (PID: $pid)..."

        # 尝试优雅停止
        kill -TERM $pid 2>/dev/null || true

        # 等待进程结束
        local count=0
        while ps -p $pid > /dev/null 2>&1 && [ $count -lt 10 ]; do
            sleep 1
            count=$((count + 1))
        done

        # 如果进程仍在运行，强制杀死
        if ps -p $pid > /dev/null 2>&1; then
            print_message $YELLOW "强制停止 $name..."
            kill -KILL $pid 2>/dev/null || true
        fi

        rm -f "$pid_file"
    fi

    # 如果提供了端口号，确保端口上的所有进程都被停止
    if [ -n "$port" ]; then
        kill_process_by_port $port "$name"
    fi

    # 最终检查
    if [ -n "$port" ] && lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_message $RED "❌ $name 端口 $port 仍被占用"
    else
        print_message $GREEN "✅ $name 已停止"
    fi
}

# 检查环境
check_environment() {
    print_title "检查运行环境"
    
    # 检查Python
    if ! check_command python3; then
        print_message $RED "请安装Python 3.8+"
        exit 1
    fi
    
    local python_version=$(python3 --version | cut -d' ' -f2)
    print_message $GREEN "✅ Python: $python_version"
    
    # 检查Node.js
    if ! check_command node; then
        print_message $RED "请安装Node.js 16+"
        exit 1
    fi
    
    local node_version=$(node --version)
    print_message $GREEN "✅ Node.js: $node_version"
    
    # 检查npm
    if ! check_command npm; then
        print_message $RED "请安装npm"
        exit 1
    fi
    
    local npm_version=$(npm --version)
    print_message $GREEN "✅ npm: $npm_version"
    
    # 检查项目目录
    if [ ! -d "$BACKEND_DIR" ]; then
        print_message $RED "❌ 后端目录不存在: $BACKEND_DIR"
        exit 1
    fi
    
    if [ ! -d "$FRONTEND_DIR" ]; then
        print_message $RED "❌ 前端目录不存在: $FRONTEND_DIR"
        exit 1
    fi
    
    print_message $GREEN "✅ 项目目录检查完成"
}

# 安装后端依赖
install_backend_deps() {
    print_title "安装后端依赖"
    
    cd "$BACKEND_DIR"
    
    # 检查requirements.txt
    if [ ! -f "requirements.txt" ]; then
        print_message $RED "❌ requirements.txt 不存在"
        exit 1
    fi
    
    # 检查虚拟环境（在项目根目录）
    if [ ! -d "$PROJECT_ROOT/.venv" ]; then
        print_message $YELLOW "创建Python虚拟环境..."
        cd "$PROJECT_ROOT"
        python3 -m venv .venv
        cd "$BACKEND_DIR"
    fi

    # 激活虚拟环境
    source "$PROJECT_ROOT/.venv/bin/activate"
    
    # 升级pip
    print_message $BLUE "升级pip..."
    pip install --upgrade pip
    
    # 安装依赖
    print_message $BLUE "安装Python依赖..."
    pip install -r requirements.txt
    
    print_message $GREEN "✅ 后端依赖安装完成"
}

# 安装前端依赖
install_frontend_deps() {
    print_title "安装前端依赖"
    
    cd "$FRONTEND_DIR"
    
    # 检查package.json
    if [ ! -f "package.json" ]; then
        print_message $RED "❌ package.json 不存在"
        exit 1
    fi
    
    # 安装依赖
    print_message $BLUE "安装Node.js依赖..."
    npm install
    
    print_message $GREEN "✅ 前端依赖安装完成"
}

# 清空上传目录
clear_uploads_directory() {
    local uploads_dir="$BACKEND_DIR/uploads"
    
    if [ -d "$uploads_dir" ]; then
        print_message $BLUE "清空上传目录: $uploads_dir"
        rm -rf "$uploads_dir"/*
        print_message $GREEN "✅ 上传目录已清空"
    else
        print_message $YELLOW "⚠️  上传目录不存在: $uploads_dir"
        mkdir -p "$uploads_dir"
        print_message $GREEN "✅ 上传目录已创建"
    fi
}

# 启动后端
start_backend() {
    print_title "启动后端服务"
    
    # 清空上传目录
    clear_uploads_directory
    
    cd "$BACKEND_DIR"
    
    # 检查端口
    if ! check_port 8000; then
        print_message $RED "❌ 后端端口8000被占用，请先停止相关进程"
        exit 1
    fi
    
    # 检查环境变量文件
    if [ ! -f ".env" ]; then
        print_message $YELLOW "⚠️  .env文件不存在"
        if [ -f ".env.example" ]; then
            print_message $BLUE "请复制.env.example为.env并配置相应变量"
            print_message $BLUE "cp .env.example .env"
        fi
        read -p "是否继续启动？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # 激活虚拟环境
    source "$PROJECT_ROOT/.venv/bin/activate"
    
    # 启动后端
    print_message $BLUE "启动FastAPI服务器..."
    nohup python3 start.py > "$BACKEND_LOG" 2>&1 &
    echo $! > "$BACKEND_PID"
    
    # 等待服务启动
    print_message $BLUE "等待后端服务启动..."
    sleep 3
    
    # 检查服务是否启动成功
    if is_process_running "$BACKEND_PID"; then
        print_message $GREEN "✅ 后端服务启动成功 (PID: $(cat $BACKEND_PID))"
        print_message $GREEN "   服务地址: http://localhost:8000"
        print_message $GREEN "   API文档: http://localhost:8000/docs"
        print_message $GREEN "   日志文件: $BACKEND_LOG"
    else
        print_message $RED "❌ 后端服务启动失败"
        print_message $RED "请查看日志: $BACKEND_LOG"
        exit 1
    fi
}

# 启动前端
start_frontend() {
    print_title "启动前端服务"
    
    cd "$FRONTEND_DIR"
    
    # 检查端口
    if ! check_port 3000; then
        print_message $YELLOW "⚠️  前端端口3000被占用，React将尝试使用其他端口"
    fi
    
    # 启动前端
    print_message $BLUE "启动React开发服务器..."
    nohup npm start > "$FRONTEND_LOG" 2>&1 &
    echo $! > "$FRONTEND_PID"
    
    # 等待服务启动
    print_message $BLUE "等待前端服务启动..."
    sleep 5
    
    # 检查服务是否启动成功
    if is_process_running "$FRONTEND_PID"; then
        print_message $GREEN "✅ 前端服务启动成功 (PID: $(cat $FRONTEND_PID))"
        print_message $GREEN "   服务地址: http://localhost:3000"
        print_message $GREEN "   日志文件: $FRONTEND_LOG"
    else
        print_message $RED "❌ 前端服务启动失败"
        print_message $RED "请查看日志: $FRONTEND_LOG"
        exit 1
    fi
}

# 停止所有服务
stop_all() {
    print_title "停止所有服务"

    stop_process "$BACKEND_PID" "后端服务" "8000"
    stop_process "$FRONTEND_PID" "前端服务" "3000"

    print_message $GREEN "✅ 所有服务已停止"
}

# 查看状态
show_status() {
    print_title "服务状态"
    
    if is_process_running "$BACKEND_PID"; then
        local backend_pid=$(cat "$BACKEND_PID")
        print_message $GREEN "✅ 后端服务运行中 (PID: $backend_pid)"
        print_message $GREEN "   服务地址: http://localhost:8000"
    else
        print_message $RED "❌ 后端服务未运行"
    fi
    
    if is_process_running "$FRONTEND_PID"; then
        local frontend_pid=$(cat "$FRONTEND_PID")
        print_message $GREEN "✅ 前端服务运行中 (PID: $frontend_pid)"
        print_message $GREEN "   服务地址: http://localhost:3000"
    else
        print_message $RED "❌ 前端服务未运行"
    fi
}

# 查看日志
show_logs() {
    local service=$1
    
    case $service in
        "backend"|"后端")
            if [ -f "$BACKEND_LOG" ]; then
                print_message $BLUE "后端日志 (最后50行):"
                tail -n 50 "$BACKEND_LOG"
            else
                print_message $YELLOW "后端日志文件不存在"
            fi
            ;;
        "frontend"|"前端")
            if [ -f "$FRONTEND_LOG" ]; then
                print_message $BLUE "前端日志 (最后50行):"
                tail -n 50 "$FRONTEND_LOG"
            else
                print_message $YELLOW "前端日志文件不存在"
            fi
            ;;
        *)
            print_message $BLUE "所有日志:"
            if [ -f "$BACKEND_LOG" ]; then
                print_message $CYAN "=== 后端日志 (最后25行) ==="
                tail -n 25 "$BACKEND_LOG"
            fi
            if [ -f "$FRONTEND_LOG" ]; then
                print_message $CYAN "=== 前端日志 (最后25行) ==="
                tail -n 25 "$FRONTEND_LOG"
            fi
            ;;
    esac
}

# 显示帮助信息
show_help() {
    print_message $CYAN "AI测试用例生成器 - 启动脚本"
    echo
    print_message $YELLOW "用法: $0 [命令]"
    echo
    print_message $BLUE "可用命令:"
    print_message $GREEN "  start     启动前后端服务 (默认)"
    print_message $GREEN "  stop      停止所有服务"
    print_message $GREEN "  restart   重启所有服务"
    print_message $GREEN "  status    查看服务状态"
    print_message $GREEN "  install   安装依赖"
    print_message $GREEN "  logs      查看日志 [backend|frontend]"
    print_message $GREEN "  help      显示帮助信息"
    echo
    print_message $BLUE "示例:"
    print_message $YELLOW "  $0              # 启动所有服务"
    print_message $YELLOW "  $0 start        # 启动所有服务"
    print_message $YELLOW "  $0 stop         # 停止所有服务"
    print_message $YELLOW "  $0 status       # 查看状态"
    print_message $YELLOW "  $0 logs backend # 查看后端日志"
    echo
}

# 主函数
main() {
    local command=${1:-start}
    
    case $command in
        "start")
            check_environment
            start_backend
            start_frontend
            echo
            print_message $GREEN "🎉 所有服务启动完成!"
            print_message $BLUE "前端地址: http://localhost:3000"
            print_message $BLUE "后端地址: http://localhost:8000"
            print_message $BLUE "API文档: http://localhost:8000/docs"
            echo
            print_message $YELLOW "使用 '$0 stop' 停止服务"
            print_message $YELLOW "使用 '$0 status' 查看状态"
            print_message $YELLOW "使用 '$0 logs' 查看日志"
            ;;
        "stop")
            stop_all
            ;;
        "restart")
            stop_all
            sleep 2
            check_environment
            start_backend
            start_frontend
            print_message $GREEN "🎉 服务重启完成!"
            ;;
        "status")
            show_status
            ;;
        "install")
            check_environment
            install_backend_deps
            install_frontend_deps
            print_message $GREEN "🎉 依赖安装完成!"
            ;;
        "logs")
            show_logs $2
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_message $RED "❌ 未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 信号处理
trap 'print_message $YELLOW "\n收到中断信号，正在停止服务..."; stop_all; exit 0' INT TERM

# 运行主函数
main "$@"
