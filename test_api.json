{"openapi": "3.0.0", "info": {"title": "用户管理API", "version": "1.0.0", "description": "简单的用户管理系统API"}, "servers": [{"url": "https://api.example.com/v1", "description": "生产环境"}], "paths": {"/users": {"get": {"summary": "获取用户列表", "description": "获取所有用户的列表", "responses": {"200": {"description": "成功返回用户列表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}}}, "post": {"summary": "创建新用户", "description": "创建一个新的用户", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUser"}}}}, "responses": {"201": {"description": "用户创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "400": {"description": "请求参数错误"}}}}, "/users/{id}": {"get": {"summary": "获取用户详情", "description": "根据ID获取用户详细信息", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功返回用户信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "404": {"description": "用户不存在"}}}}}, "components": {"schemas": {"User": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户ID"}, "name": {"type": "string", "description": "用户姓名"}, "email": {"type": "string", "format": "email", "description": "用户邮箱"}}}, "CreateUser": {"type": "object", "required": ["name", "email"], "properties": {"name": {"type": "string", "description": "用户姓名"}, "email": {"type": "string", "format": "email", "description": "用户邮箱"}}}}}}