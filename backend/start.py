#!/usr/bin/env python3
"""
测试用例生成器启动脚本
"""
import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    # 检查环境变量文件
    env_file = project_root / ".env"
    if not env_file.exists():
        print("⚠️  警告: .env 文件不存在")
        print("请复制 .env.example 为 .env 并配置相应的环境变量")
        
        example_file = project_root / ".env.example"
        if example_file.exists():
            print(f"示例配置文件位置: {example_file}")
        
        # 询问是否继续
        response = input("是否继续启动？(y/N): ").strip().lower()
        if response != 'y':
            sys.exit(1)
    
    # 导入配置和应用
    from config import settings
    from utils.logging_config import setup_logging, get_logger
    
    # 设置日志
    setup_logging()
    logger = get_logger(__name__)
    
    # 检查必要的配置
    missing_configs = []
    if not settings.qwen_api_key:
        missing_configs.append("QWEN_API_KEY")
    if not settings.deepseek_api_key:
        missing_configs.append("DEEPSEEK_API_KEY")
    
    if missing_configs:
        logger.error(f"缺少必要的配置: {', '.join(missing_configs)}")
        print(f"❌ 缺少必要的环境变量: {', '.join(missing_configs)}")
        print("请在 .env 文件中配置这些变量")
        sys.exit(1)
    
    # 创建必要的目录
    settings.create_directories()
    logger.info("目录检查完成")
    
    # 启动应用
    import uvicorn
    from main import app
    
    logger.info("🚀 启动测试用例生成器...")
    logger.info(f"服务器地址: http://{settings.host}:{settings.port}")
    logger.info(f"API文档: http://{settings.host}:{settings.port}/docs")
    logger.info(f"健康检查: http://{settings.host}:{settings.port}/api/health")
    
    # 运行服务器
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True
    )

except KeyboardInterrupt:
    print("\n👋 服务器已停止")
    sys.exit(0)
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"❌ 启动失败: {e}")
    sys.exit(1)
