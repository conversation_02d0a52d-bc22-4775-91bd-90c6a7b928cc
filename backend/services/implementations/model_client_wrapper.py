"""
模型客户端包装器
将现有的模型客户端包装为接口实现
"""
from typing import Any
from ..interfaces import IModelClient


class ModelClientWrapper(IModelClient):
    """模型客户端包装器"""
    
    def __init__(self, client: Any, model_name: str, supports_vision: bool = False):
        self._client = client
        self._model_name = model_name
        self._supports_vision = supports_vision
    
    @property
    def client(self) -> Any:
        """获取原始客户端"""
        return self._client
    
    def get_model_name(self) -> str:
        """获取模型名称"""
        return self._model_name
    
    def supports_vision(self) -> bool:
        """是否支持视觉功能"""
        return self._supports_vision
    
    def supports_streaming(self) -> bool:
        """是否支持流式输出"""
        return True  # 所有当前模型都支持流式输出
