"""
文件处理器实现
负责处理不同类型的文件
"""
from typing import Dict, Any
from PIL import Image as PILImage
from autogen_core import Image as AGImage
from ..interfaces import IFileProcessor
from ..pdf_service import pdf_service
from ..openapi_service import openapi_service
from utils.logging_config import get_logger

logger = get_logger(__name__)


class ImageFileProcessor(IFileProcessor):
    """图像文件处理器"""
    
    def supports_file_type(self, file_extension: str) -> bool:
        """检查是否支持指定的文件类型"""
        return file_extension.lower() in ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp']
    
    async def process_file(self, file_path: str) -> Dict[str, Any]:
        """处理图像文件"""
        try:
            pil_image = PILImage.open(file_path)
            ag_image = AGImage(pil_image)
            
            return {
                'type': 'image',
                'image': ag_image,
                'size': pil_image.size,
                'mode': pil_image.mode,
                'format': pil_image.format
            }
        except Exception as e:
            logger.error(f"图像文件处理失败: {str(e)}")
            raise Exception(f"图像文件处理失败: {str(e)}")


class PDFFileProcessor(IFileProcessor):
    """PDF文件处理器"""
    
    def supports_file_type(self, file_extension: str) -> bool:
        """检查是否支持指定的文件类型"""
        return file_extension.lower() == 'pdf'
    
    async def process_file(self, file_path: str) -> Dict[str, Any]:
        """处理PDF文件"""
        try:
            pdf_data = pdf_service.extract_text_from_pdf(file_path)
            return {
                'type': 'pdf',
                'content': pdf_data['text'],
                'metadata': pdf_data.get('metadata', {}),
                'extraction_method': pdf_data.get('extraction_method', 'unknown')
            }
        except Exception as e:
            logger.error(f"PDF文件处理失败: {str(e)}")
            raise Exception(f"PDF文件处理失败: {str(e)}")


class OpenAPIFileProcessor(IFileProcessor):
    """OpenAPI文件处理器"""
    
    def supports_file_type(self, file_extension: str) -> bool:
        """检查是否支持指定的文件类型"""
        return file_extension.lower() in ['json', 'yaml', 'yml']
    
    async def process_file(self, file_path: str) -> Dict[str, Any]:
        """处理OpenAPI文件"""
        try:
            api_data = openapi_service.parse_openapi_file(file_path)
            test_scenarios = openapi_service.generate_test_scenarios(api_data['api_info'])
            
            return {
                'type': 'openapi',
                'api_info': api_data['api_info'],
                'test_scenarios': test_scenarios,
                'parsed_at': api_data.get('parsed_at')
            }
        except Exception as e:
            logger.error(f"OpenAPI文件处理失败: {str(e)}")
            raise Exception(f"OpenAPI文件处理失败: {str(e)}")


class FileProcessorFactory:
    """文件处理器工厂"""
    
    def __init__(self):
        self._processors = [
            ImageFileProcessor(),
            PDFFileProcessor(),
            OpenAPIFileProcessor()
        ]
    
    def get_processor(self, file_extension: str) -> IFileProcessor:
        """根据文件扩展名获取合适的处理器"""
        for processor in self._processors:
            if processor.supports_file_type(file_extension):
                return processor
        
        raise ValueError(f"不支持的文件类型: {file_extension}")
    
    def supports_file_type(self, file_extension: str) -> bool:
        """检查是否支持指定的文件类型"""
        return any(processor.supports_file_type(file_extension) for processor in self._processors)
