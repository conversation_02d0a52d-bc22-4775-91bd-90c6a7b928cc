"""
提示词生成器实现
负责根据不同文件类型生成优化的提示词
"""
from ..interfaces import IPromptGenerator
from utils.logging_config import get_logger

logger = get_logger(__name__)


class PromptGenerator(IPromptGenerator):
    """提示词生成器实现"""
    
    def generate_prompt(self, file_type: str, content: str, context: str, requirements: str) -> str:
        """生成优化的提示词"""
        base_format = self._get_base_format()
        
        if file_type in ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp']:
            return self._generate_image_prompt(context, requirements, base_format)
        elif file_type == 'pdf':
            return self._generate_pdf_prompt(content, context, requirements, base_format)
        elif file_type in ['json', 'yaml', 'yml']:
            return self._generate_api_prompt(content, context, requirements, base_format)
        else:
            return self._generate_default_prompt(content, context, requirements, base_format)
    
    def _get_base_format(self) -> str:
        """获取基础格式要求"""
        return """
请先以 Markdown 格式生成测试用例，包含以下内容：
1. 测试用例 ID 和标题（使用二级标题格式，如 ## TC-001: 测试标题）
2. 优先级（加粗显示，如 **优先级:** 高）
3. 描述（加粗显示，如 **描述:** 测试描述）
4. 前置条件（如果有，加粗显示，如 **前置条件:** 条件描述）
5. 测试步骤和预期结果（使用标准 Markdown 表格格式）

对于测试步骤表格，请使用以下格式：

```
### 测试步骤

| # | 步骤描述 | 预期结果 |
| --- | --- | --- |
| 1 | 第一步描述 | 第一步预期结果 |
| 2 | 第二步描述 | 第二步预期结果 |
```

请确保表格格式正确，包含表头和分隔行。
"""
    
    def _generate_image_prompt(self, context: str, requirements: str, base_format: str) -> str:
        """生成图像文件的提示词"""
        return f"""请基于上传的图像生成全面的测试用例。

上下文信息: {context}

需求: {requirements}

**重要格式要求**：
请严格按照以下格式生成测试用例，这对于系统解析非常重要：

1. 每个测试用例必须以二级标题开始：## TC-001: 测试标题
2. 每个测试用例必须包含以下字段（使用加粗格式）：
   - **优先级:** 高/中/低
   - **描述:** 测试用例的详细描述
   - **前置条件:** 执行测试前的条件（如果有）

{base_format}

请确保测试用例覆盖全面，包含正向和负向测试场景，特别关注：
- UI界面的交互测试
- 用户体验测试
- 界面元素的功能测试
- 异常情况处理"""
    
    def _generate_pdf_prompt(self, content: str, context: str, requirements: str, base_format: str) -> str:
        """生成PDF文件的提示词"""
        return f"""请基于文档内容生成全面的测试用例。

{content}

用户上下文: {context}

用户需求: {requirements}

{base_format}

请确保测试用例覆盖全面，包含正向和负向测试场景。"""
    
    def _generate_api_prompt(self, content: str, context: str, requirements: str, base_format: str) -> str:
        """生成API文档的提示词"""
        return f"""请基于API文档生成全面的API测试用例。

{content}

用户上下文: {context}

用户需求: {requirements}

{base_format}

请确保测试用例覆盖全面，特别关注：
- 所有API端点的测试
- 正向测试（正常请求和响应）
- 负向测试（错误参数、认证失败等）
- 边界值测试（参数边界、数据长度等）
- 不同HTTP状态码的验证
- 请求和响应数据格式验证
- API安全性测试"""
    
    def _generate_default_prompt(self, content: str, context: str, requirements: str, base_format: str) -> str:
        """生成默认提示词"""
        return f"""请基于文档内容生成全面的测试用例。

文档内容:
{content}

上下文信息: {context}

需求: {requirements}

{base_format}

请确保测试用例覆盖全面，包含正向和负向测试场景。"""
