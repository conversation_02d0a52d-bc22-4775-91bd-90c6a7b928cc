"""
导出服务实现
负责将测试用例导出为不同格式
"""
from typing import List, Union, Dict, Any
from ..interfaces import IExportService
from ..excel_service import excel_service
from ..xmind_service import xmind_service
from models.test_case import TestCase
from utils.logging_config import get_logger

logger = get_logger(__name__)


class ExportService(IExportService):
    """导出服务实现"""
    
    def export_to_excel(self, test_cases: List[Union[TestCase, Dict[str, Any]]], filename_prefix: str = "test_cases") -> str:
        """导出到Excel文件"""
        try:
            filepath = excel_service.generate_excel(test_cases, filename_prefix)
            logger.info(f"成功导出Excel文件: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Excel导出失败: {str(e)}")
            raise Exception(f"Excel导出失败: {str(e)}")
    
    def export_to_xmind(self, test_cases: List[Union[TestCase, Dict[str, Any]]], filename_prefix: str = "test_cases") -> str:
        """导出到XMind文件"""
        try:
            filepath = xmind_service.generate_xmind(test_cases, filename_prefix)
            logger.info(f"成功导出XMind文件: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"XMind导出失败: {str(e)}")
            raise Exception(f"XMind导出失败: {str(e)}")
