"""
服务接口定义模块
定义核心服务的抽象接口，为依赖注入和解耦提供基础
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, AsyncGenerator, Optional, Union
from models.test_case import TestCase


class IModelClient(ABC):
    """AI模型客户端接口"""
    
    @abstractmethod
    def get_model_name(self) -> str:
        """获取模型名称"""
        pass
    
    @abstractmethod
    def supports_vision(self) -> bool:
        """是否支持视觉功能"""
        pass
    
    @abstractmethod
    def supports_streaming(self) -> bool:
        """是否支持流式输出"""
        pass


class IModelSelector(ABC):
    """模型选择器接口"""
    
    @abstractmethod
    def select_model_for_file(self, file_path: str, preferred_model: Optional[str] = None) -> IModelClient:
        """根据文件类型和用户偏好选择合适的模型"""
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        pass


class IFileProcessor(ABC):
    """文件处理器接口"""
    
    @abstractmethod
    def supports_file_type(self, file_extension: str) -> bool:
        """检查是否支持指定的文件类型"""
        pass
    
    @abstractmethod
    async def process_file(self, file_path: str) -> Dict[str, Any]:
        """处理文件并返回结构化数据"""
        pass


class IPromptGenerator(ABC):
    """提示词生成器接口"""
    
    @abstractmethod
    def generate_prompt(self, file_type: str, content: str, context: str, requirements: str) -> str:
        """生成优化的提示词"""
        pass


class ITestCaseGenerator(ABC):
    """测试用例生成器接口"""
    
    @abstractmethod
    async def generate_test_cases_stream(
        self,
        file_path: str,
        context: str,
        requirements: str,
        preferred_model: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """流式生成测试用例"""
        pass
    
    @abstractmethod
    def generate_mindmap_from_test_cases(self, test_cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """从测试用例生成思维导图数据"""
        pass


class IExportService(ABC):
    """导出服务接口"""
    
    @abstractmethod
    def export_to_excel(self, test_cases: List[Union[TestCase, Dict[str, Any]]], filename_prefix: str = "test_cases") -> str:
        """导出到Excel文件"""
        pass
    
    @abstractmethod
    def export_to_xmind(self, test_cases: List[Union[TestCase, Dict[str, Any]]], filename_prefix: str = "test_cases") -> str:
        """导出到XMind文件"""
        pass


class IDependencyContainer(ABC):
    """依赖注入容器接口"""
    
    @abstractmethod
    def register_singleton(self, interface_type: type, implementation: Any) -> None:
        """注册单例服务"""
        pass
    
    @abstractmethod
    def register_transient(self, interface_type: type, implementation_factory: callable) -> None:
        """注册瞬态服务"""
        pass
    
    @abstractmethod
    def resolve(self, interface_type: type) -> Any:
        """解析服务实例"""
        pass
    
    @abstractmethod
    def is_registered(self, interface_type: type) -> bool:
        """检查服务是否已注册"""
        pass
