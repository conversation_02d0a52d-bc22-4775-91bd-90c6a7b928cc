"""
服务注册模块
负责注册所有服务到依赖注入容器
"""
from .container import container
from .interfaces import (
    IModelSelector, IPromptGenerator, ITestCaseGenerator, IExportService
)
from .implementations.model_selector import ModelSelector
from .implementations.prompt_generator import PromptGenerator
from .implementations.test_case_generator import TestCaseGenerator
from .implementations.export_service import ExportService
from utils.logging_config import get_logger

logger = get_logger(__name__)


def register_services():
    """注册所有服务到依赖注入容器"""
    try:
        # 注册模型选择器（单例）
        container.register_singleton(IModelSelector, ModelSelector())
        logger.info("模型选择器服务已注册")
        
        # 注册提示词生成器（单例）
        container.register_singleton(IPromptGenerator, PromptGenerator())
        logger.info("提示词生成器服务已注册")
        
        # 注册导出服务（单例）
        container.register_singleton(IExportService, ExportService())
        logger.info("导出服务已注册")
        
        # 注册测试用例生成器（单例，需要依赖注入）
        def create_test_case_generator():
            model_selector = container.resolve(IModelSelector)
            prompt_generator = container.resolve(IPromptGenerator)
            return TestCaseGenerator(model_selector, prompt_generator)
        
        container.register_singleton(ITestCaseGenerator, create_test_case_generator)
        logger.info("测试用例生成器服务已注册")
        
        # 记录已注册的服务
        registered_services = container.get_registered_services()
        logger.info(f"服务注册完成，已注册服务: {list(registered_services.keys())}")
        
    except Exception as e:
        logger.error(f"服务注册失败: {str(e)}")
        raise


def get_service(service_type):
    """获取服务实例"""
    try:
        return container.resolve(service_type)
    except Exception as e:
        logger.error(f"获取服务失败 {service_type.__name__}: {str(e)}")
        raise


# 服务快捷访问函数
def get_model_selector() -> IModelSelector:
    """获取模型选择器服务"""
    return get_service(IModelSelector)


def get_prompt_generator() -> IPromptGenerator:
    """获取提示词生成器服务"""
    return get_service(IPromptGenerator)


def get_test_case_generator() -> ITestCaseGenerator:
    """获取测试用例生成器服务"""
    return get_service(ITestCaseGenerator)


def get_export_service() -> IExportService:
    """获取导出服务"""
    return get_service(IExportService)
