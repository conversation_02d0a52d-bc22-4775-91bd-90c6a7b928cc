import os
import xmind
import zipfile
import tempfile
from typing import List, Dict, Any, Union
from datetime import datetime
from models.test_case import TestCase

class XMindService:
    def __init__(self):
        self.results_dir = "results"
        os.makedirs(self.results_dir, exist_ok=True)

    def generate_xmind(self, test_cases: List[Union[TestCase, Dict[str, Any]]], filename_prefix: str = "test_cases") -> str:
        """
        从测试用例生成XMind文件

        参数:
            test_cases: 要包含在XMind文件中的测试用例列表
            filename_prefix: 生成的XMind文件的前缀

        返回:
            生成的XMind文件的路径
        """
        # 为文件名创建时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{timestamp}.xmind"
        filepath = os.path.join(self.results_dir, filename)

        # 创建XMind工作簿
        workbook = xmind.load(filepath)  # 创建新的工作簿
        sheet = workbook.getPrimarySheet()
        sheet.setTitle("测试用例")

        # 创建根主题
        root_topic = sheet.getRootTopic()
        root_topic.setTitle(f"测试用例总览 ({len(test_cases)}个)")

        # 按优先级分组测试用例
        priority_groups = {}
        for tc in test_cases:
            if isinstance(tc, dict):
                priority = tc.get('priority', 'Medium')
            else:
                priority = tc.priority or 'Medium'
            
            if priority not in priority_groups:
                priority_groups[priority] = []
            priority_groups[priority].append(tc)

        # 为每个优先级创建子主题
        for priority, cases in priority_groups.items():
            priority_topic = root_topic.addSubTopic()
            priority_topic.setTitle(f"{priority} 优先级 ({len(cases)}个)")

            # 为每个测试用例创建子主题
            for tc in cases:
                if isinstance(tc, dict):
                    tc_id = tc.get('id', 'TC-???')
                    tc_title = tc.get('title', '未命名测试用例')
                    tc_description = tc.get('description', '')
                    tc_preconditions = tc.get('preconditions', '')
                    tc_steps = tc.get('steps', [])
                else:
                    tc_id = tc.id
                    tc_title = tc.title
                    tc_description = tc.description
                    tc_preconditions = tc.preconditions or ''
                    tc_steps = tc.steps

                # 创建测试用例主题
                tc_topic = priority_topic.addSubTopic()
                tc_topic.setTitle(f"{tc_id}: {tc_title}")

                # 添加描述子主题
                if tc_description:
                    desc_topic = tc_topic.addSubTopic()
                    desc_topic.setTitle(f"描述: {tc_description}")

                # 添加前置条件子主题
                if tc_preconditions:
                    precond_topic = tc_topic.addSubTopic()
                    precond_topic.setTitle(f"前置条件: {tc_preconditions}")

                # 添加测试步骤子主题
                if tc_steps:
                    steps_topic = tc_topic.addSubTopic()
                    steps_topic.setTitle(f"测试步骤 ({len(tc_steps)}步)")

                    for step in tc_steps:
                        if isinstance(step, dict):
                            step_number = step.get('step_number', '?')
                            step_description = step.get('description', '')
                            step_expected = step.get('expected_result', '')
                        else:
                            step_number = step.step_number
                            step_description = step.description
                            step_expected = step.expected_result

                        # 创建步骤主题
                        step_topic = steps_topic.addSubTopic()
                        step_topic.setTitle(f"步骤{step_number}: {step_description}")

                        # 添加预期结果子主题
                        if step_expected:
                            expected_topic = step_topic.addSubTopic()
                            expected_topic.setTitle(f"预期结果: {step_expected}")

        # 添加统计信息主题
        stats_topic = root_topic.addSubTopic()
        stats_topic.setTitle("统计信息")

        # 总测试用例数
        total_topic = stats_topic.addSubTopic()
        total_topic.setTitle(f"总测试用例: {len(test_cases)}个")

        # 优先级分布
        priority_dist_topic = stats_topic.addSubTopic()
        priority_dist_topic.setTitle("优先级分布")
        for priority, cases in priority_groups.items():
            priority_count_topic = priority_dist_topic.addSubTopic()
            priority_count_topic.setTitle(f"{priority}: {len(cases)}个")

        # 平均步骤数
        total_steps = 0
        valid_cases = 0
        for tc in test_cases:
            if isinstance(tc, dict):
                steps = tc.get('steps', [])
            else:
                steps = tc.steps
            
            if steps:
                total_steps += len(steps)
                valid_cases += 1

        avg_steps = total_steps / valid_cases if valid_cases > 0 else 0
        avg_topic = stats_topic.addSubTopic()
        avg_topic.setTitle(f"平均步骤数: {avg_steps:.1f}")

        # 保存XMind文件
        xmind.save(workbook, filepath)

        # 修复XMind文件，添加缺少的META-INF/manifest.xml
        self._fix_xmind_file(filepath)

        return filepath

    def _format_text_for_xmind(self, text: str, max_length: int = 100) -> str:
        """
        格式化文本以适合XMind显示

        参数:
            text: 要格式化的文本
            max_length: 最大长度

        返回:
            格式化后的文本
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = " ".join(text.split())
        
        # 如果文本太长，截断并添加省略号
        if len(text) > max_length:
            text = text[:max_length-3] + "..."
        
        return text

    def _fix_xmind_file(self, filepath: str):
        """
        修复XMind文件，添加缺少的META-INF/manifest.xml文件

        参数:
            filepath: XMind文件路径
        """
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.xmind') as temp_file:
                temp_filepath = temp_file.name

            # 读取原始XMind文件并添加manifest.xml
            with zipfile.ZipFile(filepath, 'r') as original_zip:
                with zipfile.ZipFile(temp_filepath, 'w', zipfile.ZIP_DEFLATED) as new_zip:
                    # 复制所有原始文件
                    for item in original_zip.infolist():
                        data = original_zip.read(item.filename)
                        new_zip.writestr(item, data)

                    # 检查是否已经有META-INF/manifest.xml
                    if 'META-INF/manifest.xml' not in original_zip.namelist():
                        # 创建manifest.xml内容
                        manifest_content = '''<?xml version="1.0" encoding="UTF-8"?>
<manifest xmlns="urn:xmind:xmap:xmlns:manifest:1.0" password-hint="">
    <file-entry full-path="content.xml" media-type="text/xml"/>
    <file-entry full-path="META-INF/" media-type=""/>
    <file-entry full-path="META-INF/manifest.xml" media-type="text/xml"/>
    <file-entry full-path="styles.xml" media-type="text/xml"/>
    <file-entry full-path="comments.xml" media-type="text/xml"/>
</manifest>'''

                        # 添加manifest.xml文件
                        new_zip.writestr('META-INF/manifest.xml', manifest_content)

            # 替换原文件
            os.replace(temp_filepath, filepath)

        except Exception as e:
            # 如果修复失败，清理临时文件
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)
            raise e

# 创建全局XMind服务实例
xmind_service = XMindService()
