"""
依赖注入容器实现
管理服务实例的创建和依赖关系
"""
from typing import Dict, Any, Type, Callable, Optional
from .interfaces import IDependencyContainer
from utils.logging_config import get_logger

logger = get_logger(__name__)


class DependencyContainer(IDependencyContainer):
    """依赖注入容器实现"""
    
    def __init__(self):
        self._singletons: Dict[Type, Any] = {}
        self._transients: Dict[Type, Callable] = {}
        self._singleton_instances: Dict[Type, Any] = {}
    
    def register_singleton(self, interface_type: Type, implementation: Any) -> None:
        """注册单例服务"""
        if interface_type in self._singletons or interface_type in self._transients:
            logger.warning(f"服务 {interface_type.__name__} 已经注册，将被覆盖")
        
        self._singletons[interface_type] = implementation
        # 清除可能存在的实例缓存
        if interface_type in self._singleton_instances:
            del self._singleton_instances[interface_type]
        
        logger.debug(f"注册单例服务: {interface_type.__name__}")
    
    def register_transient(self, interface_type: Type, implementation_factory: Callable) -> None:
        """注册瞬态服务"""
        if interface_type in self._singletons or interface_type in self._transients:
            logger.warning(f"服务 {interface_type.__name__} 已经注册，将被覆盖")
        
        self._transients[interface_type] = implementation_factory
        logger.debug(f"注册瞬态服务: {interface_type.__name__}")
    
    def resolve(self, interface_type: Type) -> Any:
        """解析服务实例"""
        # 检查单例服务
        if interface_type in self._singletons:
            if interface_type not in self._singleton_instances:
                implementation = self._singletons[interface_type]
                if callable(implementation):
                    # 如果是工厂函数，调用它创建实例
                    self._singleton_instances[interface_type] = implementation()
                else:
                    # 如果是实例，直接使用
                    self._singleton_instances[interface_type] = implementation
                logger.debug(f"创建单例实例: {interface_type.__name__}")
            return self._singleton_instances[interface_type]
        
        # 检查瞬态服务
        if interface_type in self._transients:
            factory = self._transients[interface_type]
            instance = factory()
            logger.debug(f"创建瞬态实例: {interface_type.__name__}")
            return instance
        
        raise ValueError(f"未注册的服务类型: {interface_type.__name__}")
    
    def is_registered(self, interface_type: Type) -> bool:
        """检查服务是否已注册"""
        return interface_type in self._singletons or interface_type in self._transients
    
    def clear(self) -> None:
        """清除所有注册的服务"""
        self._singletons.clear()
        self._transients.clear()
        self._singleton_instances.clear()
        logger.debug("清除所有注册的服务")
    
    def get_registered_services(self) -> Dict[str, str]:
        """获取已注册服务的信息"""
        services = {}
        for service_type in self._singletons.keys():
            services[service_type.__name__] = "Singleton"
        for service_type in self._transients.keys():
            services[service_type.__name__] = "Transient"
        return services


# 全局容器实例
container = DependencyContainer()
