"""
增强的文件管理工具模块
"""
import os
import hashlib
import mimetypes
import aiofiles
from typing import List, Optional, Tuple, Dict, Any
from pathlib import Path
from datetime import datetime, timedelta
import uuid
import asyncio
from fastapi import UploadFile

from config import settings
from utils.exceptions import (
    FileError, FileSizeError, FileTypeError,
    ValidationError, handle_unexpected_error
)
from utils.logging_config import get_logger, security_logger

logger = get_logger(__name__)


class FileManager:
    """增强的文件管理器"""

    def __init__(self):
        self.upload_dir = Path(settings.upload_dir)
        self.results_dir = Path(settings.results_dir)
        self.temp_dir = Path(settings.temp_dir)
        self.max_file_size = settings.max_file_size
        self.allowed_extensions = set(settings.allowed_file_extensions)

        # 确保目录存在
        self._ensure_directories()

    def _ensure_directories(self):
        """确保所有必要目录存在"""
        for directory in [self.upload_dir, self.results_dir, self.temp_dir]:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"确保目录存在: {directory}")

    async def save_uploaded_file(
        self,
        file: UploadFile,
        client_ip: str = "unknown"
    ) -> Tuple[str, Dict[str, Any]]:
        """
        安全地保存上传的文件

        参数:
            file: FastAPI上传文件对象
            client_ip: 客户端IP地址

        返回:
            (文件路径, 文件信息字典)
        """
        try:
            # 验证文件
            file_info = await self._validate_file(file)

            # 生成安全的文件名
            safe_filename = self._generate_safe_filename(file.filename)
            file_path = self.upload_dir / safe_filename

            # 保存文件
            await self._save_file_content(file, file_path)

            # 计算文件哈希
            file_hash = await self._calculate_file_hash(file_path)

            # 更新文件信息
            file_info.update({
                'saved_path': str(file_path),
                'safe_filename': safe_filename,
                'file_hash': file_hash,
                'saved_at': datetime.now().isoformat()
            })

            # 记录成功的文件上传
            security_logger.log_file_upload(
                filename=file.filename,
                file_size=file_info['size'],
                client_ip=client_ip,
                success=True
            )

            logger.info(f"文件保存成功: {safe_filename} ({file_info['size']} bytes)")

            return str(file_path), file_info

        except Exception as e:
            # 记录失败的文件上传
            security_logger.log_file_upload(
                filename=getattr(file, 'filename', 'unknown'),
                file_size=getattr(file, 'size', 0),
                client_ip=client_ip,
                success=False
            )

            if isinstance(e, (FileError, ValidationError)):
                raise e
            else:
                raise handle_unexpected_error(e, "save_uploaded_file")

    async def _validate_file(self, file: UploadFile) -> Dict[str, Any]:
        """验证上传的文件"""
        if not file.filename:
            raise ValidationError("文件名不能为空")

        # 获取文件扩展名
        file_extension = Path(file.filename).suffix.lower()
        if not file_extension:
            raise FileTypeError("", list(self.allowed_extensions))

        # 验证文件类型
        if file_extension not in self.allowed_extensions:
            raise FileTypeError(file_extension, list(self.allowed_extensions))

        # 验证文件大小
        file_size = 0
        if hasattr(file, 'size') and file.size:
            file_size = file.size
        else:
            # 如果没有size属性，读取内容来获取大小
            content = await file.read()
            file_size = len(content)
            # 重置文件指针
            await file.seek(0)

        if file_size > self.max_file_size:
            raise FileSizeError(file_size, self.max_file_size)

        if file_size == 0:
            raise ValidationError("文件不能为空")

        # 验证MIME类型
        mime_type, _ = mimetypes.guess_type(file.filename)

        return {
            'original_filename': file.filename,
            'extension': file_extension,
            'size': file_size,
            'mime_type': mime_type,
            'content_type': file.content_type
        }

    def _generate_safe_filename(self, original_filename: str) -> str:
        """生成安全的文件名"""
        # 获取文件扩展名
        file_extension = Path(original_filename).suffix.lower()

        # 生成UUID作为文件名
        safe_name = str(uuid.uuid4())

        return f"{safe_name}{file_extension}"

    async def _save_file_content(self, file: UploadFile, file_path: Path):
        """异步保存文件内容"""
        try:
            async with aiofiles.open(file_path, 'wb') as f:
                # 分块读取和写入，避免内存问题
                chunk_size = 8192  # 8KB chunks
                while chunk := await file.read(chunk_size):
                    await f.write(chunk)
        except Exception as e:
            # 如果保存失败，清理部分文件
            if file_path.exists():
                file_path.unlink()
            raise FileError(f"保存文件失败: {str(e)}", str(e))

    async def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件的SHA256哈希值"""
        hash_sha256 = hashlib.sha256()
        try:
            async with aiofiles.open(file_path, 'rb') as f:
                while chunk := await f.read(8192):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            logger.warning(f"计算文件哈希失败: {str(e)}")
            return ""

    def cleanup_old_files(self, max_age_days: Optional[int] = None) -> List[str]:
        """
        清理旧文件

        参数:
            max_age_days: 文件最大保留天数，默认使用配置值

        返回:
            删除的文件路径列表
        """
        if max_age_days is None:
            max_age_days = settings.file_cleanup_days

        removed_files = []
        cutoff_time = datetime.now() - timedelta(days=max_age_days)

        # 清理各个目录
        for directory in [self.upload_dir, self.results_dir, self.temp_dir]:
            removed_files.extend(self._cleanup_directory(directory, cutoff_time))

        if removed_files:
            logger.info(f"清理了 {len(removed_files)} 个旧文件")

        return removed_files

    def _cleanup_directory(self, directory: Path, cutoff_time: datetime) -> List[str]:
        """清理指定目录中的旧文件"""
        removed_files = []

        if not directory.exists():
            return removed_files

        try:
            for file_path in directory.iterdir():
                if file_path.is_file():
                    # 获取文件修改时间
                    file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)

                    if file_mtime < cutoff_time:
                        try:
                            file_path.unlink()
                            removed_files.append(str(file_path))
                            logger.debug(f"删除旧文件: {file_path}")
                        except Exception as e:
                            logger.error(f"删除文件失败 {file_path}: {str(e)}")

        except Exception as e:
            logger.error(f"清理目录失败 {directory}: {str(e)}")

        return removed_files

    def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """获取文件信息"""
        path = Path(file_path)

        if not path.exists():
            return None

        try:
            stat = path.stat()
            return {
                'filename': path.name,
                'size': stat.st_size,
                'created_at': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified_at': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'extension': path.suffix.lower(),
                'mime_type': mimetypes.guess_type(str(path))[0]
            }
        except Exception as e:
            logger.error(f"获取文件信息失败 {file_path}: {str(e)}")
            return None

    def delete_file(self, file_path: str) -> bool:
        """安全删除文件"""
        path = Path(file_path)

        try:
            if path.exists() and path.is_file():
                path.unlink()
                logger.info(f"删除文件: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"删除文件失败 {file_path}: {str(e)}")
            return False


# 创建全局文件管理器实例
file_manager = FileManager()


# 保持向后兼容的函数
def save_uploaded_file(file_content: bytes, directory: str, filename: str = None) -> str:
    """
    向后兼容的文件保存函数

    注意: 建议使用 FileManager.save_uploaded_file 方法
    """
    logger.warning("使用了已弃用的 save_uploaded_file 函数，建议使用 FileManager")

    # 如果目录不存在，则创建
    os.makedirs(directory, exist_ok=True)

    # 如果没有提供文件名，则生成一个
    if filename is None:
        filename = f"{uuid.uuid4()}"

    # 保存文件
    file_path = os.path.join(directory, filename)
    with open(file_path, "wb") as f:
        f.write(file_content)

    return file_path


def clean_old_files(directory: str, max_age_days: int = 7) -> List[str]:
    """
    向后兼容的文件清理函数

    注意: 建议使用 FileManager.cleanup_old_files 方法
    """
    logger.warning("使用了已弃用的 clean_old_files 函数，建议使用 FileManager")

    import time

    # 获取当前时间
    now = time.time()

    # 用于存储删除的文件的列表
    removed_files = []

    # 检查目录是否存在
    if not os.path.exists(directory):
        return removed_files

    # 遍历目录中的文件
    for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)

        # 检查是否为文件（非目录）
        if os.path.isfile(file_path):
            # 获取文件修改时间
            file_mod_time = os.path.getmtime(file_path)

            # 计算天数
            age_days = (now - file_mod_time) / (24 * 3600)

            # 如果超过最大天数则删除
            if age_days > max_age_days:
                os.remove(file_path)
                removed_files.append(file_path)

    return removed_files
