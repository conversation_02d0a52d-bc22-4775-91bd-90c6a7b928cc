"""
统一异常处理模块
"""
from typing import Optional, Dict, Any
from fastapi import HTTPException, status
import logging

logger = logging.getLogger(__name__)


class BaseAPIException(HTTPException):
    """基础API异常类"""
    
    def __init__(
        self,
        status_code: int,
        detail: str,
        internal_detail: Optional[str] = None,
        error_code: Optional[str] = None,
        headers: Optional[Dict[str, Any]] = None
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error_code = error_code
        self.internal_detail = internal_detail
        
        # 记录内部错误详情
        if internal_detail:
            logger.error(
                f"API Exception - Code: {error_code}, Status: {status_code}, "
                f"Detail: {detail}, Internal: {internal_detail}"
            )


class ValidationError(BaseAPIException):
    """数据验证错误"""
    
    def __init__(self, detail: str, field: Optional[str] = None):
        error_detail = f"验证失败: {detail}"
        if field:
            error_detail = f"字段 '{field}' {error_detail}"
        
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=error_detail,
            error_code="VALIDATION_ERROR"
        )


class FileError(BaseAPIException):
    """文件处理错误"""
    
    def __init__(self, detail: str, internal_detail: Optional[str] = None):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"文件处理错误: {detail}",
            internal_detail=internal_detail,
            error_code="FILE_ERROR"
        )


class FileSizeError(FileError):
    """文件大小错误"""
    
    def __init__(self, file_size: int, max_size: int):
        detail = f"文件大小 {file_size / 1024 / 1024:.2f}MB 超过限制 {max_size / 1024 / 1024:.2f}MB"
        super().__init__(detail=detail)
        self.error_code = "FILE_SIZE_ERROR"


class FileTypeError(FileError):
    """文件类型错误"""
    
    def __init__(self, file_extension: str, allowed_extensions: list):
        detail = f"不支持的文件类型 '{file_extension}'，支持的类型: {', '.join(allowed_extensions)}"
        super().__init__(detail=detail)
        self.error_code = "FILE_TYPE_ERROR"


class AIServiceError(BaseAPIException):
    """AI服务错误"""
    
    def __init__(self, detail: str, internal_detail: Optional[str] = None):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI服务错误: {detail}",
            internal_detail=internal_detail,
            error_code="AI_SERVICE_ERROR"
        )


class AITimeoutError(AIServiceError):
    """AI服务超时错误"""
    
    def __init__(self, timeout_seconds: int):
        detail = f"AI服务请求超时（{timeout_seconds}秒）"
        super().__init__(detail=detail)
        self.error_code = "AI_TIMEOUT_ERROR"


class ConfigurationError(BaseAPIException):
    """配置错误"""
    
    def __init__(self, detail: str, internal_detail: Optional[str] = None):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="服务配置错误",
            internal_detail=f"Configuration error: {detail} - {internal_detail}",
            error_code="CONFIGURATION_ERROR"
        )


class ResourceNotFoundError(BaseAPIException):
    """资源未找到错误"""
    
    def __init__(self, resource_type: str, resource_id: str):
        detail = f"{resource_type} '{resource_id}' 未找到"
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_code="RESOURCE_NOT_FOUND"
        )


class RateLimitError(BaseAPIException):
    """请求频率限制错误"""
    
    def __init__(self, detail: str = "请求过于频繁，请稍后再试"):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=detail,
            error_code="RATE_LIMIT_ERROR"
        )


class ServiceUnavailableError(BaseAPIException):
    """服务不可用错误"""
    
    def __init__(self, service_name: str, internal_detail: Optional[str] = None):
        detail = f"{service_name} 服务暂时不可用"
        super().__init__(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=detail,
            internal_detail=internal_detail,
            error_code="SERVICE_UNAVAILABLE"
        )


def handle_unexpected_error(error: Exception, context: str = "") -> BaseAPIException:
    """处理未预期的错误"""
    error_detail = f"内部服务器错误"
    internal_detail = f"Unexpected error in {context}: {str(error)}"
    
    logger.exception(f"Unexpected error: {internal_detail}")
    
    return BaseAPIException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=error_detail,
        internal_detail=internal_detail,
        error_code="INTERNAL_SERVER_ERROR"
    )
