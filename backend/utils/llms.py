"""
AI模型客户端配置模块
"""
from autogen_ext.models.openai import OpenAIChatCompletionClient
from config import settings
from utils.exceptions import ConfigurationError
from utils.logging_config import get_logger

logger = get_logger(__name__)


def _setup_qwen_model_client():
    """设置Qwen模型客户端"""
    try:
        if not settings.qwen_api_key:
            raise ConfigurationError(
                "Qwen API密钥未配置",
                "QWEN_API_KEY environment variable is not set"
            )

        model_config = {
            "model": "qwen-vl-max-latest",
            "api_key": settings.qwen_api_key,
            "model_info": {
                "vision": True,
                "function_calling": True,
                "json_output": True,
                "family": "qwen",
                "multiple_system_messages": True,
                "structured_output": True
            },
            "base_url": settings.qwen_base_url
        }

        logger.info("Qwen模型客户端初始化成功")
        return OpenAIChatCompletionClient(**model_config)

    except Exception as e:
        logger.error(f"Qwen模型客户端初始化失败: {str(e)}")
        raise ConfigurationError(
            "Qwen模型配置错误",
            f"Failed to initialize Qwen client: {str(e)}"
        )


def _setup_deepseek_model_client():
    """设置DeepSeek模型客户端"""
    try:
        if not settings.deepseek_api_key:
            raise ConfigurationError(
                "DeepSeek API密钥未配置",
                "DEEPSEEK_API_KEY environment variable is not set"
            )

        model_config = {
            "model": "deepseek-chat",
            "api_key": settings.deepseek_api_key,
            "model_info": {
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": "deepseek",
                "multiple_system_messages": True,
                "structured_output": True
            },
            "base_url": settings.deepseek_base_url
        }

        logger.info("DeepSeek模型客户端初始化成功")
        return OpenAIChatCompletionClient(**model_config)

    except Exception as e:
        logger.error(f"DeepSeek模型客户端初始化失败: {str(e)}")
        raise ConfigurationError(
            "DeepSeek模型配置错误",
            f"Failed to initialize DeepSeek client: {str(e)}"
        )


def _setup_moonshot_model_client():
    """设置Moonshot模型客户端"""
    try:
        if not settings.moonshot_api_key:
            logger.warning("Moonshot API密钥未配置，跳过Moonshot模型初始化")
            return None

        model_config = {
            "model": "moonshot-v1-8k",
            "api_key": settings.moonshot_api_key,
            "model_info": {
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": "moonshot",
                "multiple_system_messages": True,
                "structured_output": True
            },
            "base_url": settings.moonshot_base_url
        }

        logger.info("Moonshot模型客户端初始化成功")
        return OpenAIChatCompletionClient(**model_config)

    except Exception as e:
        logger.error(f"Moonshot模型客户端初始化失败: {str(e)}")
        # Moonshot是可选的，不抛出异常
        logger.warning("Moonshot模型初始化失败，将跳过该模型")
        return None


# 初始化模型客户端
try:
    model_client = _setup_qwen_model_client()
    deepseek_model_client = _setup_deepseek_model_client()
    moonshot_model_client = _setup_moonshot_model_client()

    # 统计可用模型
    available_models = []
    if model_client:
        available_models.append("Qwen")
    if deepseek_model_client:
        available_models.append("DeepSeek")
    if moonshot_model_client:
        available_models.append("Moonshot")

    logger.info(f"AI模型客户端初始化完成，可用模型: {', '.join(available_models)}")
except ConfigurationError:
    # 配置错误会在应用启动时被捕获
    raise
except Exception as e:
    logger.critical(f"AI模型客户端初始化失败: {str(e)}")
    raise ConfigurationError(
        "AI模型初始化失败",
        f"Critical error during model client initialization: {str(e)}"
    )
