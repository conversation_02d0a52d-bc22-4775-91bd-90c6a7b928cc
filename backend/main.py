"""
测试用例生成器主应用
"""
import asyncio
import time
from contextlib import asynccontextmanager
from pathlib import Path
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# 导入配置和工具
from config import settings
from utils.logging_config import setup_logging, request_logger, get_logger
from utils.exceptions import BaseAPIException, handle_unexpected_error
from utils.file_utils import file_manager
from routers import test_cases

# 设置日志
setup_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("=== 测试用例生成器启动 ===")
    logger.info(f"版本: {app.version}")
    logger.info(f"调试模式: {settings.debug}")
    logger.info(f"允许的来源: {settings.allowed_origins}")

    # 确保目录存在
    settings.create_directories()

    # 启动后台任务（文件清理）
    cleanup_task = asyncio.create_task(periodic_cleanup())

    try:
        yield
    finally:
        # 关闭时执行
        logger.info("=== 测试用例生成器关闭 ===")
        cleanup_task.cancel()
        try:
            await cleanup_task
        except asyncio.CancelledError:
            pass


async def periodic_cleanup():
    """定期清理旧文件"""
    while True:
        try:
            await asyncio.sleep(24 * 3600)  # 每24小时执行一次
            removed_files = file_manager.cleanup_old_files()
            if removed_files:
                logger.info(f"定期清理完成，删除了 {len(removed_files)} 个文件")
        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error(f"定期清理失败: {str(e)}")


# 创建FastAPI应用
app = FastAPI(
    title="AI测试用例生成器",
    description="基于AI的智能测试用例生成平台，支持图像、PDF、OpenAPI文档等多种输入格式",
    version="1.0.0",
    lifespan=lifespan
)


# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求和响应"""
    start_time = time.time()

    # 获取客户端IP
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "unknown")

    # 记录请求
    request_logger.log_request(
        method=request.method,
        url=str(request.url),
        client_ip=client_ip,
        user_agent=user_agent
    )

    try:
        response = await call_next(request)

        # 计算处理时间
        process_time = time.time() - start_time

        # 记录响应
        request_logger.log_response(
            method=request.method,
            url=str(request.url),
            status_code=response.status_code,
            duration=process_time
        )

        # 添加处理时间到响应头
        response.headers["X-Process-Time"] = str(process_time)

        return response

    except Exception as e:
        # 记录错误
        request_logger.log_error(
            method=request.method,
            url=str(request.url),
            error=e
        )
        raise


# 全局异常处理器
@app.exception_handler(BaseAPIException)
async def api_exception_handler(request: Request, exc: BaseAPIException):
    """处理API异常"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "error_code": exc.error_code,
            "message": exc.detail,
            "timestamp": time.time()
        }
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """处理HTTP异常"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "error_code": "HTTP_ERROR",
            "message": exc.detail,
            "timestamp": time.time()
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """处理未捕获的异常"""
    api_exc = handle_unexpected_error(exc, f"{request.method} {request.url}")
    return JSONResponse(
        status_code=api_exc.status_code,
        content={
            "error": True,
            "error_code": api_exc.error_code,
            "message": api_exc.detail,
            "timestamp": time.time()
        }
    )


# 包含路由
app.include_router(test_cases.router)


@app.get("/")
async def root():
    """根端点"""
    return {
        "message": "欢迎使用AI测试用例生成器API",
        "version": app.version,
        "status": "running",
        "timestamp": time.time()
    }


@app.get("/api/ping")
async def ping():
    """健康检查端点"""
    return {
        "status": "success",
        "message": "pong",
        "timestamp": time.time(),
        "version": app.version
    }


@app.get("/api/health")
async def health_check():
    """详细健康检查"""
    try:
        # 检查文件系统
        file_system_ok = all([
            settings.upload_dir and Path(settings.upload_dir).exists(),
            settings.results_dir and Path(settings.results_dir).exists(),
            settings.temp_dir and Path(settings.temp_dir).exists()
        ])

        return {
            "status": "healthy",
            "timestamp": time.time(),
            "version": app.version,
            "services": {
                "file_system": "ok" if file_system_ok else "error",
                "configuration": "ok"
            },
            "configuration": {
                "max_file_size_mb": settings.max_file_size / 1024 / 1024,
                "allowed_extensions": settings.allowed_file_extensions,
                "cleanup_days": settings.file_cleanup_days
            }
        }
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "timestamp": time.time(),
                "error": str(e)
            }
        )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
