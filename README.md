# AI测试用例生成器

基于AI的智能测试用例生成平台，支持从流程图、思维导图、UI截图、PDF文档和OpenAPI规范生成高质量测试用例。

## ✨ 核心功能

- 🤖 **AI驱动**: 基于Qwen、DeepSeek和Moonshot大模型智能生成测试用例
- 📊 **多格式支持**: 支持图像、PDF、JSON、YAML等多种输入格式
- 📋 **多种输出**: 支持Excel、XMind、JSON等多种输出格式
- 🚀 **高性能**: 异步处理，支持大文件和批量操作

## 🛠️ 技术栈

**后端**: FastAPI + Python 3.8+ + AutoGen
**前端**: React 18 + Material-UI
**AI模型**: Qwen + DeepSeek + Moonshot

## 🚀 快速启动

### 环境要求
- **Python**: 3.8+
- **Node.js**: 16+
- **npm**: 最新版本

### 一键启动（推荐）

#### Linux/macOS 用户
```bash
# 首次使用：给脚本添加执行权限
chmod +x start.sh

# 安装依赖并启动服务
./start.sh install  # 首次运行
./start.sh          # 启动服务

# 其他常用命令
./start.sh status   # 查看状态
./start.sh stop     # 停止服务
./start.sh logs     # 查看日志
```

#### Windows 用户
```cmd
# 安装依赖并启动服务
start.bat install  # 首次运行
start.bat           # 启动服务

# 其他常用命令
start.bat status    # 查看状态
start.bat stop      # 停止服务
start.bat logs      # 查看日志
```

### 启动脚本命令

| 命令 | 功能 |
|------|------|
| `install` | 安装项目依赖 |
| `start` | 启动前后端服务（默认） |
| `stop` | 停止所有服务 |
| `restart` | 重启所有服务 |
| `status` | 查看服务运行状态 |
| `logs [backend\|frontend]` | 查看日志文件 |
| `help` | 显示帮助信息 |

### 配置API密钥

首次使用需要配置AI模型的API密钥：

```bash
# 复制环境变量模板
cp backend/.env.example backend/.env

# 编辑配置文件，添加API密钥
# TESTCASE_QWEN_API_KEY=your_qwen_api_key
# TESTCASE_DEEPSEEK_API_KEY=your_deepseek_api_key
# TESTCASE_MOONSHOT_API_KEY=your_moonshot_api_key  # 可选
```

### 访问地址

启动成功后访问：
- 🌐 **前端应用**: http://localhost:3000
- 🔧 **后端API**: http://localhost:8000
- 📚 **API文档**: http://localhost:8000/docs

## 🔧 手动启动（开发调试）

如需手动启动进行开发调试：

### 后端启动
```bash
cd backend
python3 -m venv .venv
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows
pip install -r requirements.txt
python start.py
```

### 前端启动
```bash
cd frontend
npm install
npm start
```

## ⚙️ 主要配置

### 环境变量

| 变量名 | 必需 | 说明 |
|--------|------|------|
| `TESTCASE_QWEN_API_KEY` | ✅ | Qwen API密钥 |
| `TESTCASE_DEEPSEEK_API_KEY` | ✅ | DeepSeek API密钥 |
| `TESTCASE_MOONSHOT_API_KEY` | ❌ | Moonshot API密钥(可选) |
| `TESTCASE_PORT` | ❌ | 服务器端口(默认8000) |
| `TESTCASE_MAX_FILE_SIZE` | ❌ | 最大文件大小(默认50MB) |

### 主要API端点

- `POST /api/generate-test-cases` - 生成测试用例
- `GET /api/health` - 健康检查
- `GET /docs` - API文档

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 停止服务
   ./start.sh stop  # 或 start.bat stop

   # 查看端口占用
   lsof -i :8000  # macOS/Linux
   netstat -ano | findstr :8000  # Windows
   ```

2. **API密钥错误**
   - 检查 `backend/.env` 文件中的API密钥配置
   - 确保密钥格式正确且有效

3. **依赖安装失败**
   ```bash
   # 重新安装依赖
   ./start.sh install  # 或 start.bat install
   ```

4. **查看日志**
   ```bash
   ./start.sh logs          # 查看所有日志
   ./start.sh logs backend  # 查看后端日志
   ./start.sh logs frontend # 查看前端日志
   ```

---

**技术支持**: 如遇问题请查看日志文件
**课程咨询**: 微信 huice666
